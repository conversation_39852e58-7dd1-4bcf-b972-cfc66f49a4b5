{"cells": [{"cell_type": "code", "execution_count": 1, "id": "7ae95606", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import os\n", "from dotenv import load_dotenv\n", "load_dotenv()\n"]}, {"cell_type": "code", "execution_count": 2, "id": "c8f3c76b", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Agentic2.0'"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["os.getenv(\"LANGCHAIN_PROJECT\")"]}, {"cell_type": "code", "execution_count": 3, "id": "4dd7e631", "metadata": {}, "outputs": [{"data": {"text/plain": ["'***************************************************'"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["os.getenv(\"LANGCHAIN_API_KEY\")"]}, {"cell_type": "code", "execution_count": 4, "id": "3a95342a", "metadata": {}, "outputs": [], "source": ["os.environ[\"OPENAI_API_KEY\"]=os.getenv(\"OPENAI_API_KEY\")\n", "os.environ[\"GROQ_API_KEY\"]=os.getenv(\"GROQ_API_KEY\")\n", "\n", "## Langsmith Tracking And Tracing\n", "os.environ[\"LANGCHAIN_API_KEY\"]=os.getenv(\"LANGCHAIN_API_KEY\")\n", "os.environ[\"LANGCHAIN_PROJECT\"]=os.getenv(\"LANGCHAIN_PROJECT\")\n", "os.environ[\"LANGCHAIN_TRACING_V2\"]=\"true\""]}, {"cell_type": "code", "execution_count": 5, "id": "47124a66", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["client=<openai.resources.chat.completions.completions.Completions object at 0x10380fe20> async_client=<openai.resources.chat.completions.completions.AsyncCompletions object at 0x10b015d20> root_client=<openai.OpenAI object at 0x10380f190> root_async_client=<openai.AsyncOpenAI object at 0x10b015c60> model_kwargs={} openai_api_key=SecretStr('**********')\n"]}], "source": ["from langchain_openai import ChatOpenAI\n", "\n", "llm = ChatOpenAI(model=\"gpt-3.5-turbo\")\n", "print(llm)\n"]}, {"cell_type": "code", "execution_count": 6, "id": "35ca146d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["content='Agentic AI refers to artificial intelligence systems that are designed to operate independently and make decisions on their own, similar to how a human agent would. These systems are capable of reasoning, problem-solving, learning, and decision-making without the need for constant human input or intervention. Agentic AI is often used in applications such as autonomous vehicles, robotics, and intelligent personal assistants.' additional_kwargs={'refusal': None} response_metadata={'token_usage': {'completion_tokens': 74, 'prompt_tokens': 12, 'total_tokens': 86, 'completion_tokens_details': {'accepted_prediction_tokens': 0, 'audio_tokens': 0, 'reasoning_tokens': 0, 'rejected_prediction_tokens': 0}, 'prompt_tokens_details': {'audio_tokens': 0, 'cached_tokens': 0}}, 'model_name': 'gpt-3.5-turbo-0125', 'system_fingerprint': None, 'id': 'chatcmpl-Bf3UCKubq7Or6zJr5Mtkj9LdcbDfS', 'service_tier': 'default', 'finish_reason': 'stop', 'logprobs': None} id='run--6ad728ee-2b99-446b-99eb-fc23e15e2c75-0' usage_metadata={'input_tokens': 12, 'output_tokens': 74, 'total_tokens': 86, 'input_token_details': {'audio': 0, 'cache_read': 0}, 'output_token_details': {'audio': 0, 'reasoning': 0}}\n"]}], "source": ["result=llm.invoke(\"What is Agentic AI\")\n", "print(result)"]}, {"cell_type": "code", "execution_count": 7, "id": "41d6b3c9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Agentic AI refers to artificial intelligence systems that are designed to operate independently and make decisions on their own, similar to how a human agent would. These systems are capable of reasoning, problem-solving, learning, and decision-making without the need for constant human input or intervention. Agentic AI is often used in applications such as autonomous vehicles, robotics, and intelligent personal assistants.\n"]}], "source": ["print(result.content)"]}, {"cell_type": "code", "execution_count": 8, "id": "8357dd50", "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='\\n<think>\\nOkay, the user greeted me with \"Hi My name is <PERSON><PERSON>.\" I need to respond politely. Let me start by acknowledging their greeting and name. Then I should offer assistance. Maybe say something like, \"Hello <PERSON><PERSON>! Nice to meet you. How can I assist you today?\" That sounds friendly and opens the conversation for them to state their needs. I should keep it simple and not overcomplicate. Let me check if there\\'s anything else I need to consider. Since they just introduced themselves, there\\'s no specific query yet. So a general response inviting them to ask for help is appropriate here. I\\'ll go with that.\\n</think>\\n\\nHello Krish! Nice to meet you. How can I assist you today?', additional_kwargs={}, response_metadata={'token_usage': {'completion_tokens': 146, 'prompt_tokens': 15, 'total_tokens': 161, 'completion_time': 0.36041948, 'prompt_time': 0.002882686, 'queue_time': 0.250209613, 'total_time': 0.363302166}, 'model_name': 'qwen-qwq-32b', 'system_fingerprint': 'fp_1e88ca32eb', 'finish_reason': 'stop', 'logprobs': None}, id='run--d663cdde-8af6-4904-9bcb-83c57906d96f-0', usage_metadata={'input_tokens': 15, 'output_tokens': 146, 'total_tokens': 161})"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_groq import ChatGroq\n", "model=ChatGroq(model=\"qwen-qwq-32b\")\n", "model.invoke(\"Hi My name is <PERSON><PERSON>\")"]}, {"cell_type": "code", "execution_count": 9, "id": "88e170b6", "metadata": {}, "outputs": [{"data": {"text/plain": ["ChatPromptTemplate(input_variables=['input'], input_types={}, partial_variables={}, messages=[SystemMessagePromptTemplate(prompt=PromptTemplate(input_variables=[], input_types={}, partial_variables={}, template='You are an expert AI Engineer. Provide me answer based on the question'), additional_kwargs={}), HumanMessagePromptTemplate(prompt=PromptTemplate(input_variables=['input'], input_types={}, partial_variables={}, template='{input}'), additional_kwargs={})])"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["### Prompt Engineering\n", "from langchain_core.prompts import ChatPromptTemplate\n", "\n", "prompt=ChatPromptTemplate.from_messages(\n", "    [\n", "        (\"system\",\"You are an expert AI Engineer. Provide me answer based on the question\"),\n", "        (\"user\",\"{input}\")\n", "    ]\n", ")\n", "prompt\n", "\n"]}, {"cell_type": "code", "execution_count": 10, "id": "8d414adc", "metadata": {}, "outputs": [{"data": {"text/plain": ["ChatGroq(client=<groq.resources.chat.completions.Completions object at 0x10b391090>, async_client=<groq.resources.chat.completions.AsyncCompletions object at 0x10380e7a0>, model_name='gemma2-9b-it', model_kwargs={}, groq_api_key=SecretStr('**********'))"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_groq import ChatGroq\n", "model=ChatGroq(model=\"gemma2-9b-it\")\n", "model"]}, {"cell_type": "code", "execution_count": 11, "id": "2d40b1a8", "metadata": {}, "outputs": [{"data": {"text/plain": ["ChatPromptTemplate(input_variables=['input'], input_types={}, partial_variables={}, messages=[SystemMessagePromptTemplate(prompt=PromptTemplate(input_variables=[], input_types={}, partial_variables={}, template='You are an expert AI Engineer. Provide me answer based on the question'), additional_kwargs={}), HumanMessagePromptTemplate(prompt=PromptTemplate(input_variables=['input'], input_types={}, partial_variables={}, template='{input}'), additional_kwargs={})])\n", "| ChatGroq(client=<groq.resources.chat.completions.Completions object at 0x10b391090>, async_client=<groq.resources.chat.completions.AsyncCompletions object at 0x10380e7a0>, model_name='gemma2-9b-it', model_kwargs={}, groq_api_key=SecretStr('**********'))"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["### chaining\n", "chain=prompt|model\n", "chain"]}, {"cell_type": "code", "execution_count": 12, "id": "7a293571", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Let's talk about <PERSON><PERSON>!\n", "\n", "**Langsmith: Your Open-Source Toolkit for Fine-Tuning LLMs**\n", "\n", "Langsmith is an open-source platform developed by the Hugging Face team that simplifies the process of fine-tuning large language models (LLMs) for specific tasks.  \n", "\n", "**Why is <PERSON><PERSON> Important?**\n", "\n", "* **Democratization of AI:** It empowers anyone, regardless of their technical expertise, to customize powerful LLMs for their unique needs.\n", "\n", "* **Efficiency and Speed:** Langsmith streamlines the fine-tuning workflow, making it faster and more efficient than traditional methods.\n", "\n", "* **Flexibility and Customization:** It provides a variety of tools and options for customizing the fine-tuning process, allowing you to tailor it to your specific use case.\n", "\n", "**Key Features:**\n", "\n", "* **User-Friendly Interface:** Langsmith offers a web-based interface that is intuitive and easy to use, even for beginners.\n", "* **Pre-trained Models:** It provides access to a wide range of pre-trained LLMs from the Hugging Face Model Hub, ready for fine-tuning.\n", "* **Fine-Tuning Techniques:** <PERSON><PERSON> supports various fine-tuning techniques, including supervised fine-tuning, prompt engineering, and reinforcement learning.\n", "* **Dataset Management:** It simplifies the management of training datasets, allowing you to easily upload, preprocess, and evaluate your data.\n", "* **Experiment Tracking:** <PERSON><PERSON> helps you track and compare different fine-tuning experiments, making it easier to find the best-performing model.\n", "\n", "**Use Cases:**\n", "\n", "* **Text Generation:** Fine-tune LLMs for tasks like creative writing, summarization, dialogue generation, and more.\n", "* **Code Generation:** Train models to generate code in specific programming languages.\n", "* **Question Answering:** Build systems that can answer questions accurately based on a given context.\n", "* **Chatbots:** Create conversational agents that can interact with users in a natural and engaging way.\n", "* **Translation:** Fine-tune models for accurate machine translation between languages.\n", "\n", "**Getting Started:**\n", "\n", "To begin using <PERSON><PERSON>, visit the [Hugging Face website](https://huggingface.co/docs/langsmith/) and explore the documentation. They provide comprehensive guides, tutorials, and examples to help you get started with fine-tuning LLMs.\n", "\n", "\n", "\n", "Let me know if you have any more specific questions about <PERSON><PERSON>!\n", "\n"]}], "source": ["response=chain.invoke({\"input\":\"Can you tell me something about <PERSON><PERSON>\"})\n", "print(response.content)"]}, {"cell_type": "code", "execution_count": 13, "id": "6694a12e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Let's talk about <PERSON><PERSON>! You're in good hands asking an AI engineer about this – I'm familiar with its ins and outs. \n", "\n", "**Langsmith is an open-source framework designed to simplify the process of developing and deploying AI chatbots and large language models (LLMs).**\n", "\n", "Think of it as a toolkit built specifically for working with these powerful language models. \n", "\n", "Here's what makes <PERSON><PERSON> stand out:\n", "\n", "* **User-Friendliness:**  It's built with ease of use in mind. Even if you're not a deep machine learning expert, <PERSON><PERSON>'s intuitive interface and clear documentation make it accessible to a wider range of developers.\n", "\n", "* **Modular Design:** <PERSON><PERSON> breaks down the complexities of LLM development into manageable modules. This allows you to pick and choose the components you need for your project, whether it's fine-tuning a pre-trained model, building a conversational interface, or integrating with other tools.\n", "\n", "* **Streamlined Workflow:** It provides a streamlined workflow for common LLM tasks. This means you can spend less time on setup and more time focused on building your chatbot's intelligence and capabilities.\n", "\n", "* **Community-Driven:** Being open-source means Langsmith thrives on contributions from the community. This leads to constant improvements, new features, and a wealth of shared knowledge and support.\n", "\n", "**Key Features:**\n", "\n", "* **Model Management:** Easily load, fine-tune, and manage different LLM models.\n", "* **Prompt Engineering:**  Tools and techniques to craft effective prompts that elicit desired responses from your models.\n", "* **Conversational Design:**  Build conversational flows and dialogues for your chatbot.\n", "* **Data Pipelines:**  Streamline the process of gathering, cleaning, and preparing data for training and fine-tuning.\n", "* **Integration:** Connect Langsmith with other platforms and services to extend its functionality.\n", "\n", "**Who Should <PERSON>?**\n", "\n", "* **Developers:**  Anyone interested in building AI-powered chatbots, assistants, or other LLM applications.\n", "* **Researchers:**  Researchers exploring new techniques in natural language processing and conversational AI.\n", "* **Educators:**  Langsmith can be a valuable tool for teaching and learning about LLMs and AI development.\n", "\n", "\n", "Let me know if you have any more specific questions about <PERSON><PERSON>. I'm happy to delve deeper into its features, use cases, or anything else you'd like to know!\n", "\n"]}], "source": ["### OutputParser\n", "from langchain_core.output_parsers import StrOutputParser\n", "\n", "output_parser=StrOutputParser()\n", "\n", "chain=prompt|model|output_parser\n", "\n", "response=chain.invoke({\"input\":\"Can you tell me about <PERSON><PERSON>\"})\n", "print(response)"]}, {"cell_type": "code", "execution_count": 14, "id": "0221a0c5", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Return a JSON object.'"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_core.output_parsers import JsonOutputParser\n", "\n", "output_parser=JsonOutputParser()\n", "output_parser.get_format_instructions()"]}, {"cell_type": "code", "execution_count": 15, "id": "66da8aa1", "metadata": {}, "outputs": [], "source": ["### OutputParser\n", "from langchain_core.output_parsers import JsonOutputParser\n", "from langchain_core.prompts import PromptTemplate\n", "\n", "output_parser=JsonOutputParser()\n", "\n", "prompt=PromptTemplate(\n", "    template=\"Answer the user query \\n {format_instruction}\\n {query}\\n \",\n", "    input_variables=[\"query\"],\n", "    partial_variables={\"format_instruction\":output_parser.get_format_instructions()},\n", ")"]}, {"cell_type": "code", "execution_count": 16, "id": "5fe079e8", "metadata": {}, "outputs": [{"data": {"text/plain": ["PromptTemplate(input_variables=['query'], input_types={}, partial_variables={'format_instruction': 'Return a JSON object.'}, template='Answer the user query \\n {format_instruction}\\n {query}\\n ')"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["prompt"]}, {"cell_type": "code", "execution_count": 17, "id": "52a5b27c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'name': '<PERSON><PERSON>', 'description': '<PERSON>smith is an open-source framework for building and deploying large language models (LLMs). It provides tools for training, fine-tuning, and serving LLMs, as well as a library of pre-trained models.', 'key_features': ['Open-source and accessible to everyone', 'Modular and extensible design', 'Supports various LLM architectures', 'Offers tools for data preprocessing, training, and evaluation', 'Provides a platform for deploying and serving LLMs', 'Includes a library of pre-trained models'], 'website': 'https://github.com/langsmithai/langsmith'}\n"]}], "source": ["chain=prompt|model|output_parser\n", "response=chain.invoke({\"query\":\"Can you tell me about Lang<PERSON>?\"})\n", "print(response)\n", "\n"]}, {"cell_type": "code", "execution_count": 18, "id": "7ee96082", "metadata": {}, "outputs": [{"data": {"text/plain": ["ChatPromptTemplate(input_variables=['input'], input_types={}, partial_variables={}, messages=[SystemMessagePromptTemplate(prompt=PromptTemplate(input_variables=[], input_types={}, partial_variables={}, template='You are an expert AI Engineer.Provide the response in json.Provide me answer based on the question'), additional_kwargs={}), HumanMessagePromptTemplate(prompt=PromptTemplate(input_variables=['input'], input_types={}, partial_variables={}, template='{input}'), additional_kwargs={})])"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["### Assisgnment ---Chatprompttemplate\n", "\n", "### Prompt Engineering\n", "from langchain_core.prompts import ChatPromptTemplate\n", "\n", "prompt=ChatPromptTemplate.from_messages(\n", "    [\n", "        (\"system\",\"You are an expert AI Engineer.Provide the response in json.Provide me answer based on the question\"),\n", "        (\"user\",\"{input}\")\n", "    ]\n", ")\n", "prompt"]}, {"cell_type": "code", "execution_count": 19, "id": "ed7d7e82", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'response': \"Langsmith is an open-source platform designed to make it easier to develop and deploy AI applications, particularly those involving large language models (LLMs). \\n\\nHere are some key features and aspects of <PERSON><PERSON>:\\n\\n* **Focus on Developer Experience:** <PERSON><PERSON> prioritizes a streamlined and intuitive development environment for AI practitioners. It aims to simplify the process of working with LLMs, allowing developers to focus on building applications rather than getting bogged down in technical complexities.\\n\\n* **Modular and Extensible:** The platform is built with a modular architecture, enabling users to easily integrate different components and tools. This allows for customization and flexibility to suit specific project needs.\\n\\n* **Open-Source Nature:** Being open source, <PERSON><PERSON> encourages community contributions, collaboration, and transparency. This fosters innovation and allows developers to adapt and extend the platform to their specific use cases.\\n\\n* **Integration with LLMs:** <PERSON>smith provides tools and interfaces for connecting with various LLMs, including popular models like OpenAI's GPT models. This simplifies the process of incorporating LLM capabilities into applications.\\n\\n* **Deployment and Management:** <PERSON>smith offers features to facilitate the deployment and management of AI applications. This can include options for hosting, scaling, and monitoring models in production environments.\\n\\n* **Community and Support:** <PERSON>smith benefits from an active community of developers and users who contribute to its development, provide support, and share best practices.\\n\\n**In essence, <PERSON>smith aims to democratize access to AI development by providing a user-friendly and comprehensive platform that empowers developers to build and deploy innovative LLM-powered applications.**\"}\n"]}], "source": ["chain=prompt|model|output_parser\n", "response=chain.invoke({\"input\":\"Can you tell me about <PERSON><PERSON>?\"})\n", "print(response)"]}, {"cell_type": "markdown", "id": "50822936", "metadata": {}, "source": ["### Assigments: https://python.langchain.com/docs/how_to/#prompt-templates"]}, {"cell_type": "code", "execution_count": 20, "id": "0c1c1802", "metadata": {}, "outputs": [{"data": {"text/plain": ["ChatPromptTemplate(input_variables=['input'], input_types={}, partial_variables={}, messages=[SystemMessagePromptTemplate(prompt=PromptTemplate(input_variables=[], input_types={}, partial_variables={}, template='You are an expert AI Engineer.<response><answer>Your answer here</answer></response>.Provide me answer based on the question'), additional_kwargs={}), HumanMessagePromptTemplate(prompt=PromptTemplate(input_variables=['input'], input_types={}, partial_variables={}, template='{input}'), additional_kwargs={})])"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["### OutputParser\n", "from langchain_core.output_parsers import XMLOutputParser\n", "output_parser=XMLOutputParser()\n", "from langchain_core.prompts import ChatPromptTemplate\n", "\n", "prompt=ChatPromptTemplate.from_messages(\n", "    [\n", "        (\"system\",\"You are an expert AI Engineer.<response><answer>Your answer here</answer></response>.Provide me answer based on the question\"),\n", "        (\"user\",\"{input}\")\n", "    ]\n", ")\n", "prompt"]}, {"cell_type": "code", "execution_count": 21, "id": "0ca6e8b7", "metadata": {}, "outputs": [{"data": {"text/plain": ["PromptTemplate(input_variables=['query'], input_types={}, partial_variables={'format_instruction': 'The output should be formatted as a XML file.\\n1. Output should conform to the tags below.\\n2. If tags are not given, make them on your own.\\n3. Remember to always open and close all the tags.\\n\\nAs an example, for the tags [\"foo\", \"bar\", \"baz\"]:\\n1. String \"<foo>\\n   <bar>\\n      <baz></baz>\\n   </bar>\\n</foo>\" is a well-formatted instance of the schema.\\n2. String \"<foo>\\n   <bar>\\n   </foo>\" is a badly-formatted instance.\\n3. String \"<foo>\\n   <tag>\\n   </tag>\\n</foo>\" is a badly-formatted instance.\\n\\nHere are the output tags:\\n```\\nNone\\n```'}, template='Answer the user query \\n {format_instruction}\\n {query}\\n ')"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["### OutputParser\n", "from langchain_core.output_parsers import XMLOutputParser\n", "from langchain_core.prompts import PromptTemplate\n", "\n", "output_parser=XMLOutputParser()\n", "\n", "prompt=PromptTemplate(\n", "    template=\"Answer the user query \\n {format_instruction}\\n {query}\\n \",\n", "    input_variables=[\"query\"],\n", "    partial_variables={\"format_instruction\":output_parser.get_format_instructions()},\n", ")\n", "prompt"]}, {"cell_type": "code", "execution_count": 22, "id": "940f704a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["content='<LanguageModel>\\n  <Name><PERSON><PERSON></Name>\\n  <Description><PERSON><PERSON> is an open-weights AI assistant developed by Google DeepMind. It is designed to be highly customizable and adaptable, allowing users to fine-tune its capabilities for specific tasks.</Description>\\n  <Capabilities>\\n    <Capability>Text Generation</Capability>\\n    <Capability>Dialogue</Capability>\\n    <Capability>Code Generation</Capability>\\n    <Capability>Translation</Capability>\\n  </Capabilities>\\n  <Availability>Open-weights</Availability>\\n</LanguageModel> \\n' additional_kwargs={} response_metadata={'token_usage': {'completion_tokens': 126, 'prompt_tokens': 195, 'total_tokens': 321, 'completion_time': 0.229090909, 'prompt_time': 0.009446045, 'queue_time': 0.24678551599999998, 'total_time': 0.238536954}, 'model_name': 'gemma2-9b-it', 'system_fingerprint': 'fp_10c08bf97d', 'finish_reason': 'stop', 'logprobs': None} id='run--056c6d16-b316-425b-94b1-10836ab6c8ff-0' usage_metadata={'input_tokens': 195, 'output_tokens': 126, 'total_tokens': 321}\n"]}], "source": ["chain=prompt|model\n", "response=chain.invoke({\"query\":\"Can you tell me about Lang<PERSON>?\"})\n", "print(response)"]}, {"cell_type": "code", "execution_count": 23, "id": "1eec50bc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["content='<response><answer>LangChain is an open-source framework designed to simplify the development of applications powered by large language models (LLMs). It provides tools and components to streamline tasks like prompt engineering, chaining together different LLMs and other AI services, managing memory, and integrating with external data sources. Essentially, Lang<PERSON>hain acts as a scaffolding for building sophisticated LLM-based applications.</answer></response>\\n' additional_kwargs={} response_metadata={'token_usage': {'completion_tokens': 85, 'prompt_tokens': 39, 'total_tokens': 124, 'completion_time': 0.154545455, 'prompt_time': 0.002341609, 'queue_time': 0.24307678500000002, 'total_time': 0.156887064}, 'model_name': 'gemma2-9b-it', 'system_fingerprint': 'fp_10c08bf97d', 'finish_reason': 'stop', 'logprobs': None} id='run--eb7fa43c-e1a2-4fef-804a-d9d3010888fb-0' usage_metadata={'input_tokens': 39, 'output_tokens': 85, 'total_tokens': 124}\n"]}], "source": ["##output parser\n", "#from langchain_core.output_parsers import XMLOutputParser\n", "from langchain.output_parsers.xml import XMLOutputParser\n", "\n", "# XML Output Parser\n", "output_parser = XMLOutputParser()\n", "\n", "# Prompt that instructs the model to return XML\n", "prompt = ChatPromptTemplate.from_messages([\n", "    (\"system\", \"You are a helpful assistant. Respond in this XML format: <response><answer>Your answer here</answer></response>\"),\n", "    (\"human\", \"{input}\")\n", "])\n", "\n", "# Build the chain\n", "chain = prompt | model\n", "\n", "# Run the chain\n", "#response = chain.invoke({\"input\": \"What is <PERSON><PERSON><PERSON><PERSON>?\"})\n", "\n", "raw_output =chain.invoke({\"input\": \"What is <PERSON><PERSON><PERSON><PERSON>?\"})\n", "\n", "# Print result\n", "print(raw_output)\n"]}, {"cell_type": "code", "execution_count": 24, "id": "ab7431f6", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'setup': 'Why did the scarecrow win an award?',\n", " 'punchline': 'Because he was outstanding in his field!'}"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["## With Pydantic\n", "from langchain_core.output_parsers import JsonOutputParser\n", "from langchain_core.prompts import PromptTemplate\n", "from langchain_openai import ChatOpenAI\n", "from pydantic import BaseModel, Field\n", "\n", "model = ChatOpenAI(temperature=0.7)\n", "\n", "\n", "# Define your desired data structure.\n", "class Joke(BaseModel):\n", "    setup: str = Field(description=\"question to set up a joke\")\n", "    punchline: str = Field(description=\"answer to resolve the joke\")\n", "\n", "\n", "# And a query intented to prompt a language model to populate the data structure.\n", "joke_query = \"Tell me a joke.\"\n", "\n", "# Set up a parser + inject instructions into the prompt template.\n", "parser = JsonOutputParser(pydantic_object=Joke)\n", "\n", "prompt = PromptTemplate(\n", "    template=\"Answer the user query.\\n{format_instructions}\\n{query}\\n\",\n", "    input_variables=[\"query\"],\n", "    partial_variables={\"format_instructions\": parser.get_format_instructions()},\n", ")\n", "\n", "chain = prompt | model | parser\n", "\n", "chain.invoke({\"query\": joke_query})"]}, {"cell_type": "code", "execution_count": 25, "id": "36e1dcd5", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'joke': \"Why couldn't the bicycle stand up by itself? Because it was two tired!\"}"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["### Without Pydantic\n", "joke_query = \"Tell me a joke .\"\n", "\n", "parser = JsonOutputParser()\n", "\n", "prompt = PromptTemplate(\n", "    template=\"Answer the user query.\\n{format_instructions}\\n{query}\\n\",\n", "    input_variables=[\"query\"],\n", "    partial_variables={\"format_instructions\": parser.get_format_instructions()},\n", ")\n", "\n", "chain = prompt | model | parser\n", "\n", "chain.invoke({\"query\": joke_query})"]}, {"cell_type": "code", "execution_count": 26, "id": "8f2ec0ad", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<movie>Big</movie>\n", "<movie>Saving Private <PERSON></movie>\n", "<movie><PERSON></movie>\n", "<movie>Cast Away</movie>\n", "<movie>The Green Mile</movie>\n", "<movie>Philadelphia</movie>\n", "<movie>Apollo 13</movie>\n", "<movie>Sully</movie>\n", "<movie>Toy Story (franchise)</movie>\n", "<movie>Sleepless in Seattle</movie>\n"]}], "source": ["\n", "from langchain_core.output_parsers import XMLOutputParser\n", "from langchain_core.prompts import PromptTemplate\n", "\n", "\n", "actor_query = \"Generate the shortened filmography for <PERSON>.\"\n", "\n", "output = model.invoke(\n", "    f\"\"\"{actor_query}\n", "Please enclose the movies in <movie></movie> tags\"\"\"\n", ")\n", "\n", "print(output.content)"]}, {"cell_type": "code", "execution_count": 27, "id": "c90caccf", "metadata": {}, "outputs": [{"data": {"text/plain": ["Jo<PERSON>(setup='Why did the scarecrow win an award?', punchline='Because he was outstanding in his field!')"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain.output_parsers import YamlOutputParser\n", "from langchain_core.prompts import PromptTemplate\n", "from langchain_openai import ChatOpenAI\n", "from pydantic import BaseModel, Field\n", "\n", "\n", "# Define your desired data structure.\n", "class Joke(BaseModel):\n", "    setup: str = Field(description=\"question to set up a joke\")\n", "    punchline: str = Field(description=\"answer to resolve the joke\")\n", "\n", "\n", "model = ChatOpenAI(temperature=0.5)\n", "\n", "# And a query intented to prompt a language model to populate the data structure.\n", "joke_query = \"Tell me a joke.\"\n", "\n", "# Set up a parser + inject instructions into the prompt template.\n", "parser = YamlOutputParser(pydantic_object=Joke)\n", "\n", "prompt = PromptTemplate(\n", "    template=\"Answer the user query.\\n{format_instructions}\\n{query}\\n\",\n", "    input_variables=[\"query\"],\n", "    partial_variables={\"format_instructions\": parser.get_format_instructions()},\n", ")\n", "\n", "chain = prompt | model | parser\n", "\n", "chain.invoke({\"query\": joke_query})"]}, {"cell_type": "markdown", "id": "4dfed2d4", "metadata": {}, "source": ["### Assisgment:\n", "Create a simple assistant that uses any LLM and should be pydantic, when we ask about any product it should give you two information product Name, product details tentative price in USD (integer). use chat Prompt Template.\n"]}, {"cell_type": "markdown", "id": "a2999f98", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "agentic", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}