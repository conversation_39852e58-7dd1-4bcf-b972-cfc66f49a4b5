{"cells": [{"cell_type": "code", "execution_count": null, "id": "7ae95606", "metadata": {}, "outputs": [], "source": ["import os\n", "from dotenv import load_dotenv\n", "load_dotenv()\n"]}, {"cell_type": "code", "execution_count": null, "id": "c8f3c76b", "metadata": {}, "outputs": [], "source": ["os.getenv(\"LANGCHAIN_PROJECT\")"]}, {"cell_type": "code", "execution_count": null, "id": "4dd7e631", "metadata": {}, "outputs": [], "source": ["os.getenv(\"LANGCHAIN_API_KEY\")"]}, {"cell_type": "code", "execution_count": null, "id": "3a95342a", "metadata": {}, "outputs": [], "source": ["os.environ[\"OPENAI_API_KEY\"]=os.getenv(\"OPENAI_API_KEY\")\n", "os.environ[\"GROQ_API_KEY\"]=os.getenv(\"GROQ_API_KEY\")\n", "\n", "## Langsmith Tracking And Tracing\n", "os.environ[\"LANGCHAIN_API_KEY\"]=os.getenv(\"LANGCHAIN_API_KEY\")\n", "os.environ[\"LANGCHAIN_PROJECT\"]=os.getenv(\"LANGCHAIN_PROJECT\")\n", "os.environ[\"LANGCHAIN_TRACING_V2\"]=\"true\""]}, {"cell_type": "code", "execution_count": null, "id": "47124a66", "metadata": {}, "outputs": [], "source": ["from langchain_openai import ChatOpenAI\n", "\n", "llm=ChatOpenAI(model=\"o1-mini\")\n", "print(llm)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "35ca146d", "metadata": {}, "outputs": [], "source": ["result=llm.invoke(\"What is Agentic AI\")\n", "print(result)"]}, {"cell_type": "code", "execution_count": null, "id": "41d6b3c9", "metadata": {}, "outputs": [], "source": ["print(result.content)"]}, {"cell_type": "code", "execution_count": null, "id": "8357dd50", "metadata": {}, "outputs": [], "source": ["from langchain_groq import ChatGroq\n", "model=ChatGroq(model=\"qwen-qwq-32b\")\n", "model.invoke(\"Hi My name is <PERSON><PERSON>\")"]}, {"cell_type": "code", "execution_count": null, "id": "88e170b6", "metadata": {}, "outputs": [], "source": ["### Prompt Engineering\n", "from langchain_core.prompts import ChatPromptTemplate\n", "\n", "prompt=ChatPromptTemplate.from_messages(\n", "    [\n", "        (\"system\",\"You are an expert AI Engineer. Provide me answer based on the question\"),\n", "        (\"user\",\"{input}\")\n", "    ]\n", ")\n", "prompt\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "8d414adc", "metadata": {}, "outputs": [], "source": ["from langchain_groq import ChatGroq\n", "model=ChatGroq(model=\"gemma2-9b-it\")\n", "model"]}, {"cell_type": "code", "execution_count": null, "id": "2d40b1a8", "metadata": {}, "outputs": [], "source": ["### chaining\n", "chain=prompt|model\n", "chain"]}, {"cell_type": "code", "execution_count": null, "id": "7a293571", "metadata": {}, "outputs": [], "source": ["response=chain.invoke({\"input\":\"Can you tell me something about <PERSON><PERSON>\"})\n", "print(response.content)"]}, {"cell_type": "code", "execution_count": null, "id": "6694a12e", "metadata": {}, "outputs": [], "source": ["### OutputParser\n", "from langchain_core.output_parsers import StrOutputParser\n", "\n", "output_parser=StrOutputParser()\n", "\n", "chain=prompt|model|output_parser\n", "\n", "response=chain.invoke({\"input\":\"Can you tell me about <PERSON><PERSON>\"})\n", "print(response)"]}, {"cell_type": "code", "execution_count": null, "id": "0221a0c5", "metadata": {}, "outputs": [], "source": ["from langchain_core.output_parsers import JsonOutputParser\n", "\n", "output_parser=JsonOutputParser()\n", "output_parser.get_format_instructions()"]}, {"cell_type": "code", "execution_count": null, "id": "66da8aa1", "metadata": {}, "outputs": [], "source": ["### OutputParser\n", "from langchain_core.output_parsers import JsonOutputParser\n", "from langchain_core.prompts import PromptTemplate\n", "\n", "output_parser=JsonOutputParser()\n", "\n", "prompt=PromptTemplate(\n", "    template=\"Answer the user query \\n {format_instruction}\\n {query}\\n \",\n", "    input_variables=[\"query\"],\n", "    partial_variables={\"format_instruction\":output_parser.get_format_instructions()},\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "5fe079e8", "metadata": {}, "outputs": [], "source": ["prompt"]}, {"cell_type": "code", "execution_count": null, "id": "52a5b27c", "metadata": {}, "outputs": [], "source": ["chain=prompt|model|output_parser\n", "response=chain.invoke({\"query\":\"Can you tell me about Lang<PERSON>?\"})\n", "print(response)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "7ee96082", "metadata": {}, "outputs": [], "source": ["### Assisgnment ---Chatprompttemplate\n", "\n", "### Prompt Engineering\n", "from langchain_core.prompts import ChatPromptTemplate\n", "\n", "prompt=ChatPromptTemplate.from_messages(\n", "    [\n", "        (\"system\",\"You are an expert AI Engineer.Provide the response in json.Provide me answer based on the question\"),\n", "        (\"user\",\"{input}\")\n", "    ]\n", ")\n", "prompt"]}, {"cell_type": "code", "execution_count": null, "id": "ed7d7e82", "metadata": {}, "outputs": [], "source": ["chain=prompt|model|output_parser\n", "response=chain.invoke({\"input\":\"Can you tell me about <PERSON><PERSON>?\"})\n", "print(response)"]}, {"cell_type": "markdown", "id": "50822936", "metadata": {}, "source": ["### Assigments: https://python.langchain.com/docs/how_to/#prompt-templates"]}, {"cell_type": "code", "execution_count": null, "id": "0c1c1802", "metadata": {}, "outputs": [], "source": ["### OutputParser\n", "from langchain_core.output_parsers import XMLOutputParser\n", "output_parser=XMLOutputParser()\n", "from langchain_core.prompts import ChatPromptTemplate\n", "\n", "prompt=ChatPromptTemplate.from_messages(\n", "    [\n", "        (\"system\",\"You are an expert AI Engineer.<response><answer>Your answer here</answer></response>.Provide me answer based on the question\"),\n", "        (\"user\",\"{input}\")\n", "    ]\n", ")\n", "prompt"]}, {"cell_type": "code", "execution_count": null, "id": "0ca6e8b7", "metadata": {}, "outputs": [], "source": ["### OutputParser\n", "from langchain_core.output_parsers import XMLOutputParser\n", "from langchain_core.prompts import PromptTemplate\n", "\n", "output_parser=XMLOutputParser()\n", "\n", "prompt=PromptTemplate(\n", "    template=\"Answer the user query \\n {format_instruction}\\n {query}\\n \",\n", "    input_variables=[\"query\"],\n", "    partial_variables={\"format_instruction\":output_parser.get_format_instructions()},\n", ")\n", "prompt"]}, {"cell_type": "code", "execution_count": null, "id": "940f704a", "metadata": {}, "outputs": [], "source": ["chain=prompt|model\n", "response=chain.invoke({\"query\":\"Can you tell me about Lang<PERSON>?\"})\n", "print(response)"]}, {"cell_type": "code", "execution_count": null, "id": "1eec50bc", "metadata": {}, "outputs": [], "source": ["##output parser\n", "#from langchain_core.output_parsers import XMLOutputParser\n", "from langchain.output_parsers.xml import XMLOutputParser\n", "\n", "# XML Output Parser\n", "output_parser = XMLOutputParser()\n", "\n", "# Prompt that instructs the model to return XML\n", "prompt = ChatPromptTemplate.from_messages([\n", "    (\"system\", \"You are a helpful assistant. Respond in this XML format: <response><answer>Your answer here</answer></response>\"),\n", "    (\"human\", \"{input}\")\n", "])\n", "\n", "# Build the chain\n", "chain = prompt | model\n", "\n", "# Run the chain\n", "#response = chain.invoke({\"input\": \"What is <PERSON><PERSON><PERSON><PERSON>?\"})\n", "\n", "raw_output =chain.invoke({\"input\": \"What is <PERSON><PERSON><PERSON><PERSON>?\"})\n", "\n", "# Print result\n", "print(raw_output)\n"]}, {"cell_type": "code", "execution_count": null, "id": "ab7431f6", "metadata": {}, "outputs": [], "source": ["## With Pydantic\n", "from langchain_core.output_parsers import JsonOutputParser\n", "from langchain_core.prompts import PromptTemplate\n", "from langchain_openai import ChatOpenAI\n", "from pydantic import BaseModel, Field\n", "\n", "model = ChatOpenAI(temperature=0.7)\n", "\n", "\n", "# Define your desired data structure.\n", "class Joke(BaseModel):\n", "    setup: str = Field(description=\"question to set up a joke\")\n", "    punchline: str = Field(description=\"answer to resolve the joke\")\n", "\n", "\n", "# And a query intented to prompt a language model to populate the data structure.\n", "joke_query = \"Tell me a joke.\"\n", "\n", "# Set up a parser + inject instructions into the prompt template.\n", "parser = JsonOutputParser(pydantic_object=Joke)\n", "\n", "prompt = PromptTemplate(\n", "    template=\"Answer the user query.\\n{format_instructions}\\n{query}\\n\",\n", "    input_variables=[\"query\"],\n", "    partial_variables={\"format_instructions\": parser.get_format_instructions()},\n", ")\n", "\n", "chain = prompt | model | parser\n", "\n", "chain.invoke({\"query\": joke_query})"]}, {"cell_type": "code", "execution_count": null, "id": "36e1dcd5", "metadata": {}, "outputs": [], "source": ["### Without Pydantic\n", "joke_query = \"Tell me a joke .\"\n", "\n", "parser = JsonOutputParser()\n", "\n", "prompt = PromptTemplate(\n", "    template=\"Answer the user query.\\n{format_instructions}\\n{query}\\n\",\n", "    input_variables=[\"query\"],\n", "    partial_variables={\"format_instructions\": parser.get_format_instructions()},\n", ")\n", "\n", "chain = prompt | model | parser\n", "\n", "chain.invoke({\"query\": joke_query})"]}, {"cell_type": "code", "execution_count": null, "id": "8f2ec0ad", "metadata": {}, "outputs": [], "source": ["\n", "from langchain_core.output_parsers import XMLOutputParser\n", "from langchain_core.prompts import PromptTemplate\n", "\n", "\n", "actor_query = \"Generate the shortened filmography for <PERSON>.\"\n", "\n", "output = model.invoke(\n", "    f\"\"\"{actor_query}\n", "Please enclose the movies in <movie></movie> tags\"\"\"\n", ")\n", "\n", "print(output.content)"]}, {"cell_type": "code", "execution_count": null, "id": "c90caccf", "metadata": {}, "outputs": [], "source": ["from langchain.output_parsers import YamlOutputParser\n", "from langchain_core.prompts import PromptTemplate\n", "from langchain_openai import ChatOpenAI\n", "from pydantic import BaseModel, Field\n", "\n", "\n", "# Define your desired data structure.\n", "class Joke(BaseModel):\n", "    setup: str = Field(description=\"question to set up a joke\")\n", "    punchline: str = Field(description=\"answer to resolve the joke\")\n", "\n", "\n", "model = ChatOpenAI(temperature=0.5)\n", "\n", "# And a query intented to prompt a language model to populate the data structure.\n", "joke_query = \"Tell me a joke.\"\n", "\n", "# Set up a parser + inject instructions into the prompt template.\n", "parser = YamlOutputParser(pydantic_object=Joke)\n", "\n", "prompt = PromptTemplate(\n", "    template=\"Answer the user query.\\n{format_instructions}\\n{query}\\n\",\n", "    input_variables=[\"query\"],\n", "    partial_variables={\"format_instructions\": parser.get_format_instructions()},\n", ")\n", "\n", "chain = prompt | model | parser\n", "\n", "chain.invoke({\"query\": joke_query})"]}, {"cell_type": "markdown", "id": "4dfed2d4", "metadata": {}, "source": ["### Assisgment:\n", "Create a simple assistant that uses any LLM and should be pydantic, when we ask about any product it should give you two information product Name, product details tentative price in USD (integer). use chat Prompt Template.\n"]}, {"cell_type": "markdown", "id": "a2999f98", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "agentic-2", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.18"}}, "nbformat": 4, "nbformat_minor": 5}