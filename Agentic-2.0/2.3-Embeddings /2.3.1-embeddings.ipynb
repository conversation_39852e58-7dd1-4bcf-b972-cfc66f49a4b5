{"cells": [{"cell_type": "markdown", "id": "d9962def", "metadata": {}, "source": ["### Embeddings  Techniques\n", "Convert Text Into Vectors"]}, {"cell_type": "code", "execution_count": 23, "id": "cbcc35d6", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["import os\n", "from dotenv import load_dotenv\n", "load_dotenv() \n", "#https://python.langchain.com/docs/integrations/text_embedding/"]}, {"cell_type": "code", "execution_count": 3, "id": "207963af", "metadata": {}, "outputs": [], "source": ["os.environ[\"OPENAI_API_KEY\"]=os.getenv(\"OPENAI_API_KEY\")"]}, {"cell_type": "code", "execution_count": 4, "id": "63a9f244", "metadata": {}, "outputs": [], "source": ["from langchain_openai import OpenAIEmbeddings\n", "\n", "embeddings = OpenAIEmbeddings(model=\"text-embedding-3-large\")"]}, {"cell_type": "code", "execution_count": 5, "id": "0444e85e", "metadata": {}, "outputs": [{"data": {"text/plain": ["OpenAIEmbeddings(client=<openai.resources.embeddings.Embeddings object at 0x00000210B576AF90>, async_client=<openai.resources.embeddings.AsyncEmbeddings object at 0x00000210B576B8C0>, model='text-embedding-3-large', dimensions=None, deployment='text-embedding-ada-002', openai_api_version=None, openai_api_base=None, openai_api_type=None, openai_proxy=None, embedding_ctx_length=8191, openai_api_key=SecretStr('**********'), openai_organization=None, allowed_special=None, disallowed_special=None, chunk_size=1000, max_retries=2, request_timeout=None, headers=None, tiktoken_enabled=True, tiktoken_model_name=None, show_progress_bar=False, model_kwargs={}, skip_empty=False, default_headers=None, default_query=None, retry_min_seconds=4, retry_max_seconds=20, http_client=None, http_async_client=None, check_embedding_ctx_length=True)"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["embeddings"]}, {"cell_type": "code", "execution_count": 6, "id": "f022a902", "metadata": {}, "outputs": [{"data": {"text/plain": ["[-0.004184810444712639,\n", " 0.040880631655454636,\n", " -0.01898585818707943,\n", " -0.04030923917889595,\n", " 0.022583043202757835,\n", " 0.001123308320529759,\n", " 0.0123369125649333,\n", " 0.052204620093107224,\n", " -0.010375992394983768,\n", " 0.0049574910663068295,\n", " -0.01716778799891472,\n", " 0.006382728926837444,\n", " -0.01020717155188322,\n", " -0.011642149649560452,\n", " 0.011187631636857986,\n", " 0.01821967214345932,\n", " 0.022349290549755096,\n", " 0.04929570481181145,\n", " -0.018011892214417458,\n", " -0.02042732946574688,\n", " 0.002418684074655175,\n", " 0.006921657361090183,\n", " -0.04981515556573868,\n", " -0.03960798308253288,\n", " 0.02627113088965416,\n", " -0.01981697790324688,\n", " 0.010460402816534042,\n", " 0.0390365868806839,\n", " -0.008869591169059277,\n", " 0.05184100568294525,\n", " 0.012661567889153957,\n", " -0.011343466117978096,\n", " 0.0028975512832403183,\n", " -0.0020502000115811825,\n", " -0.015349716879427433,\n", " 0.018998844549059868,\n", " 0.042802594602108,\n", " 0.032985009253025055,\n", " -0.04038715735077858,\n", " 0.023894650861620903,\n", " 0.029400810599327087,\n", " 0.003788730828091502,\n", " 0.0022758357226848602,\n", " -0.021583104506134987,\n", " -0.05119169503450394,\n", " -0.027426904067397118,\n", " 0.008142362348735332,\n", " -0.025777654722332954,\n", " -0.012349898926913738,\n", " -0.025478970259428024,\n", " 0.02490757778286934,\n", " -0.01207069493830204,\n", " 0.003192987758666277,\n", " -0.013726438395678997,\n", " -0.007161902263760567,\n", " 0.021076641976833344,\n", " 0.013466713950037956,\n", " -0.005976909305900335,\n", " 0.04228314384818077,\n", " 0.01828460395336151,\n", " 0.007350202649831772,\n", " -0.0014528337633237243,\n", " -0.009291643276810646,\n", " -0.01950530894100666,\n", " -0.014947143383324146,\n", " -0.016583407297730446,\n", " 0.02094677835702896,\n", " 0.0007471136632375419,\n", " -0.021764911711215973,\n", " 0.02436215616762638,\n", " 0.0032790214754641056,\n", " 1.261210854863748e-05,\n", " -0.013921231962740421,\n", " -0.006785301957279444,\n", " 0.0012296331115067005,\n", " 0.014817281626164913,\n", " 0.03864700347185135,\n", " 0.011174645274877548,\n", " -0.021011710166931152,\n", " 0.029634563252329826,\n", " 0.04082868620753288,\n", " -0.015687357634305954,\n", " 0.029400810599327087,\n", " 0.017804112285375595,\n", " 0.04807500168681145,\n", " 0.024699797853827477,\n", " -0.010181199759244919,\n", " -0.03051762655377388,\n", " 0.007616420276463032,\n", " 0.019297529011964798,\n", " -0.05745105445384979,\n", " 0.011038290336728096,\n", " -0.020609136670827866,\n", " 0.006097032222896814,\n", " -0.0005170140066184103,\n", " -0.004139358643442392,\n", " 0.00643467390909791,\n", " -0.004015989601612091,\n", " -0.001563216676004231,\n", " 0.04093257710337639,\n", " 0.011843436397612095,\n", " -0.014622488059103489,\n", " -0.008018992841243744,\n", " 0.002699511358514428,\n", " -0.017648277804255486,\n", " -0.004658808000385761,\n", " 0.023401174694299698,\n", " 0.010713635012507439,\n", " 0.017349595203995705,\n", " 0.024621879681944847,\n", " 0.02589453011751175,\n", " -0.000484142656205222,\n", " -0.022297346964478493,\n", " 0.05186697840690613,\n", " -0.008447538129985332,\n", " -0.0006525577628053725,\n", " -0.0008441045647487044,\n", " -0.013869286514818668,\n", " 0.0027384699787944555,\n", " -0.032829172909259796,\n", " 0.00683075375854969,\n", " -0.018492382019758224,\n", " 0.013596576638519764,\n", " 0.010285088792443275,\n", " 0.0008781933574937284,\n", " 0.04716596379876137,\n", " -0.011031797155737877,\n", " 0.01044092420488596,\n", " 0.020102674141526222,\n", " -0.008109896443784237,\n", " 0.026362033560872078,\n", " 0.002493354957550764,\n", " -0.010142240673303604,\n", " 0.052126701921224594,\n", " 0.03755616024136543,\n", " 0.011103221215307713,\n", " -0.015128950588405132,\n", " 0.03672504052519798,\n", " 0.02802427113056183,\n", " 0.04804902896285057,\n", " -0.02953067235648632,\n", " -0.005184749606996775,\n", " -0.023336244747042656,\n", " -0.0031605223193764687,\n", " -0.019180651754140854,\n", " -0.005801595281809568,\n", " -0.03441349416971207,\n", " 0.020751984789967537,\n", " -0.016908062621951103,\n", " -0.0059411972761154175,\n", " -0.041400082409381866,\n", " 0.003983524162322283,\n", " 0.014947143383324146,\n", " 0.0228427667170763,\n", " 0.012570664286613464,\n", " -0.009577340446412563,\n", " -0.002379725454375148,\n", " -0.0016557435737922788,\n", " 0.02238824963569641,\n", " 0.005256174132227898,\n", " 0.044828444719314575,\n", " -0.020557193085551262,\n", " 0.019933853298425674,\n", " 0.0063048116862773895,\n", " 0.004963984247297049,\n", " 0.004431548994034529,\n", " 0.020206565037369728,\n", " 0.0023634927347302437,\n", " -0.06768419593572617,\n", " 0.026388006284832954,\n", " -0.0030696187168359756,\n", " 0.019998785108327866,\n", " 0.06311304867267609,\n", " 0.011603190563619137,\n", " -0.0514514185488224,\n", " -0.008031979203224182,\n", " 0.015895137563347816,\n", " 0.007187874987721443,\n", " -0.015349716879427433,\n", " 0.009603312239050865,\n", " -0.023037560284137726,\n", " 0.012408336624503136,\n", " 0.04394537955522537,\n", " 0.013005702756345272,\n", " -0.00063551333732903,\n", " 0.00508410669863224,\n", " 0.04412718862295151,\n", " 0.02066108211874962,\n", " 0.022609015926718712,\n", " -0.03566017001867294,\n", " 0.015427634119987488,\n", " -0.008109896443784237,\n", " -0.012375871650874615,\n", " -0.004434795584529638,\n", " 0.030881240963935852,\n", " -0.03895867243409157,\n", " -0.017284663394093513,\n", " 0.020777957513928413,\n", " -0.014518598094582558,\n", " 0.010005885735154152,\n", " -0.016440559178590775,\n", " -0.058126337826251984,\n", " 0.0008104215376079082,\n", " -0.0020761725027114153,\n", " 0.00764888571575284,\n", " 0.03950409218668938,\n", " -0.0271152351051569,\n", " -0.022076580673456192,\n", " 0.0076748584397137165,\n", " -0.02406347170472145,\n", " 0.0320499986410141,\n", " -0.03877686336636543,\n", " -0.020596150308847427,\n", " -0.04295842722058296,\n", " 0.030335819348692894,\n", " 0.02285575307905674,\n", " -0.043529823422431946,\n", " 0.023855693638324738,\n", " -0.022076580673456192,\n", " 0.00656778272241354,\n", " -0.01366150751709938,\n", " 0.04560761898756027,\n", " 0.037244491279125214,\n", " -0.032907091081142426,\n", " -0.036854904145002365,\n", " -0.013843314722180367,\n", " -0.009986406192183495,\n", " -0.02573869563639164,\n", " -0.020284481346607208,\n", " 0.005041901487857103,\n", " 0.01393421832472086,\n", " 0.01377838384360075,\n", " 0.040049515664577484,\n", " -0.008376114070415497,\n", " 0.01777813956141472,\n", " -0.030465681105852127,\n", " 0.030647488310933113,\n", " -0.017063897103071213,\n", " -0.008090417832136154,\n", " 0.045218031853437424,\n", " -0.01981697790324688,\n", " 0.023582981899380684,\n", " -0.007720310240983963,\n", " -0.02534910850226879,\n", " -0.015544509515166283,\n", " 0.05958079546689987,\n", " -0.021141571924090385,\n", " 0.011486315168440342,\n", " -0.010220157913863659,\n", " 0.00027575434069149196,\n", " 0.020479274913668633,\n", " 0.024011528119444847,\n", " 0.021388310939073563,\n", " 0.013057648204267025,\n", " -0.038750890642404556,\n", " 0.034517381340265274,\n", " 0.051815032958984375,\n", " -0.010427937842905521,\n", " -0.002361869439482689,\n", " -0.010888949036598206,\n", " 0.010492868721485138,\n", " 0.04698415845632553,\n", " 0.02186880074441433,\n", " 0.005558103788644075,\n", " -0.008239759132266045,\n", " -0.032387640327215195,\n", " 0.0265048835426569,\n", " -0.02688148245215416,\n", " 0.008915042504668236,\n", " -0.0038276894483715296,\n", " 0.008908549323678017,\n", " 0.006687905173748732,\n", " 0.042568840086460114,\n", " 0.03228375315666199,\n", " 0.026297103613615036,\n", " -0.0072203404270112514,\n", " 0.0024170607794076204,\n", " -0.020609136670827866,\n", " 0.02527119219303131,\n", " -0.04625692963600159,\n", " 0.022881725803017616,\n", " -0.024180348962545395,\n", " 0.0209597647190094,\n", " 0.02285575307905674,\n", " -0.020907821133732796,\n", " -0.02490757778286934,\n", " -0.04773736000061035,\n", " 0.029426783323287964,\n", " -0.058645784854888916,\n", " -0.029816370457410812,\n", " -0.0096747362986207,\n", " 0.0003124810173176229,\n", " 0.01988190785050392,\n", " 0.0402572937309742,\n", " -0.011843436397612095,\n", " 0.030699433758854866,\n", " -0.021284420043230057,\n", " -0.022881725803017616,\n", " -0.01912870816886425,\n", " -0.024803686887025833,\n", " -0.019648157060146332,\n", " 0.02779051847755909,\n", " -0.03584197908639908,\n", " -0.02498549409210682,\n", " 0.00484061473980546,\n", " 0.04054298996925354,\n", " 0.006119757890701294,\n", " 0.0279463529586792,\n", " -0.035114750266075134,\n", " 0.0382574163377285,\n", " 0.022686932235956192,\n", " -0.012622609734535217,\n", " -0.005126311909407377,\n", " -0.03960798308253288,\n", " 0.008129375986754894,\n", " -0.006765822414308786,\n", " 0.021401297301054,\n", " 0.007019054144620895,\n", " 0.02019357867538929,\n", " -0.025907516479492188,\n", " 0.02937483787536621,\n", " -0.023206381127238274,\n", " -0.04843861609697342,\n", " 0.011784997768700123,\n", " 0.007395654451102018,\n", " -0.01770022325217724,\n", " 0.03311486914753914,\n", " 0.005610048770904541,\n", " -0.004477000795304775,\n", " -0.028647609055042267,\n", " -0.021596090868115425,\n", " -0.061294976621866226,\n", " -0.00658726179972291,\n", " -0.005580829456448555,\n", " 0.005700952373445034,\n", " -0.024842645972967148,\n", " -0.006097032222896814,\n", " 0.001333522843196988,\n", " 0.0013530021533370018,\n", " -0.01192135363817215,\n", " -0.005058133974671364,\n", " -0.03446543961763382,\n", " -0.004106893204152584,\n", " -0.027193153277039528,\n", " -0.013181016780436039,\n", " -0.008531948551535606,\n", " 0.03810157999396324,\n", " 0.03960798308253288,\n", " -0.003100460860878229,\n", " -0.00040602238732390106,\n", " 0.0007990585872903466,\n", " 0.016414586454629898,\n", " 0.02354402281343937,\n", " -0.024271251633763313,\n", " -0.009096849709749222,\n", " -0.037763938307762146,\n", " -0.035114750266075134,\n", " 0.006882698740810156,\n", " -0.033971961587667465,\n", " 0.02854372002184391,\n", " -0.028725527226924896,\n", " 0.009194246493279934,\n", " -0.0048243822529911995,\n", " -0.011031797155737877,\n", " -0.016583407297730446,\n", " -0.027452876791357994,\n", " -0.023959582671523094,\n", " -0.030543597415089607,\n", " 0.027348987758159637,\n", " 0.023037560284137726,\n", " 0.0007970294682309031,\n", " 0.009804598987102509,\n", " -0.033530429005622864,\n", " -0.0024316704366356134,\n", " -0.017492443323135376,\n", " -0.03734837844967842,\n", " 0.00021934544201940298,\n", " -0.02308950573205948,\n", " 0.019245583564043045,\n", " 0.022076580673456192,\n", " 0.0015445490134879947,\n", " -0.004129619337618351,\n", " 0.006876205559819937,\n", " 0.01370046567171812,\n", " 0.019076762720942497,\n", " 0.00952539499849081,\n", " -0.004860094282776117,\n", " -0.0019430636893957853,\n", " -0.01647951826453209,\n", " 0.01867418922483921,\n", " 0.030699433758854866,\n", " -0.05578881874680519,\n", " -0.003577704541385174,\n", " -0.024349169805645943,\n", " 0.03828338906168938,\n", " 0.008057951927185059,\n", " -0.016427572816610336,\n", " -0.00994095392525196,\n", " 0.0032043508253991604,\n", " 0.04298439994454384,\n", " -0.04721790924668312,\n", " -0.0203494131565094,\n", " -0.020920805633068085,\n", " 0.007999514229595661,\n", " -0.003025790210813284,\n", " -0.011018810793757439,\n", " -0.03202402591705322,\n", " -0.01753140240907669,\n", " 0.031114991754293442,\n", " 0.03036179021000862,\n", " -0.015025060623884201,\n", " -0.01097335945814848,\n", " -0.032153889536857605,\n", " 0.006408701650798321,\n", " 0.005405515432357788,\n", " -0.007622913457453251,\n", " -0.03467321768403053,\n", " -0.007155409082770348,\n", " -0.007557982113212347,\n", " 0.00010500579082872719,\n", " -0.005051641259342432,\n", " -0.011194124817848206,\n", " 0.030024148523807526,\n", " -0.002019357867538929,\n", " -0.026673704385757446,\n", " 0.019076762720942497,\n", " 0.0016443806234747171,\n", " 0.03119290992617607,\n", " -0.03924436867237091,\n", " -0.04654262587428093,\n", " -0.0014974739169701934,\n", " -0.011090234853327274,\n", " 0.040517017245292664,\n", " 0.008804659359157085,\n", " -0.05228253826498985,\n", " -0.019245583564043045,\n", " -0.019167665392160416,\n", " 0.017804112285375595,\n", " -0.037166573107242584,\n", " 0.004603616427630186,\n", " -0.03529655560851097,\n", " -0.014180956408381462,\n", " -0.006405455060303211,\n", " -0.017427511513233185,\n", " -0.04875028505921364,\n", " 0.015960069373250008,\n", " 0.0032985007856041193,\n", " -0.041867583990097046,\n", " 0.03662114962935448,\n", " -0.05360713228583336,\n", " -0.029063168913125992,\n", " 0.0168561190366745,\n", " -0.08041069656610489,\n", " -0.052957821637392044,\n", " 0.009187753312289715,\n", " -0.006398961879312992,\n", " 0.003535499330610037,\n", " 0.02473875693976879,\n", " -0.014843253418803215,\n", " -0.03778991103172302,\n", " 0.0047821770422160625,\n", " -0.0075839548371732235,\n", " 0.03332265093922615,\n", " -0.0030306598637253046,\n", " 0.02224540151655674,\n", " -0.025530915707349777,\n", " -0.0035095270723104477,\n", " -0.03999757021665573,\n", " -0.010817524045705795,\n", " 0.031608469784259796,\n", " -0.04054298996925354,\n", " -0.016219792887568474,\n", " 0.06352860480546951,\n", " 0.05259420722723007,\n", " 0.005119818728417158,\n", " -0.011226590722799301,\n", " -0.018998844549059868,\n", " -0.008966987952589989,\n", " 0.012875840999186039,\n", " 0.0054931724444031715,\n", " 0.019258569926023483,\n", " 0.02141428366303444,\n", " 0.026517868041992188,\n", " -0.010220157913863659,\n", " -0.008304690010845661,\n", " 0.027297042310237885,\n", " 0.04241300746798515,\n", " -0.011590205132961273,\n", " -0.0074930512346327305,\n", " 0.02580362744629383,\n", " -0.007564475294202566,\n", " 0.041867583990097046,\n", " -0.015999028459191322,\n", " -0.03994562476873398,\n", " 0.0012085303897038102,\n", " -0.04545178264379501,\n", " -0.007973541505634785,\n", " -0.004671793896704912,\n", " 0.03514072299003601,\n", " 0.04373760148882866,\n", " -0.027348987758159637,\n", " -0.002460889285430312,\n", " -0.011129193939268589,\n", " 0.00789562426507473,\n", " 0.0025631559547036886,\n", " 0.036023784428834915,\n", " -0.0017271677497774363,\n", " -0.003775744466111064,\n", " -0.0011192501988261938,\n", " 0.026933427900075912,\n", " 0.007590447552502155,\n", " -0.018946900963783264,\n", " 0.002568025840446353,\n", " 0.006288578733801842,\n", " -0.004632835276424885,\n", " 0.01905078999698162,\n", " 0.0004934765165671706,\n", " 0.021829841658473015,\n", " 0.021764911711215973,\n", " 0.0008700769976712763,\n", " -0.012330419383943081,\n", " 0.014414708130061626,\n", " 0.010129254311323166,\n", " 0.03778991103172302,\n", " -0.038595058023929596,\n", " 0.034283630549907684,\n", " 0.0076748584397137165,\n", " -0.01127853523939848,\n", " -0.03210194408893585,\n", " -0.014401721768081188,\n", " -0.017882030457258224,\n", " 0.00975265447050333,\n", " -0.02307651937007904,\n", " -0.02119351737201214,\n", " -0.0073112440295517445,\n", " 0.006999574601650238,\n", " 0.016609380021691322,\n", " 0.016219792887568474,\n", " 0.006746343336999416,\n", " -0.00751902349293232,\n", " -0.009999392554163933,\n", " 0.02703731693327427,\n", " 0.00038512269384227693,\n", " 0.005113325547426939,\n", " -0.02171296626329422,\n", " 0.006337277125567198,\n", " 0.02786843664944172,\n", " -0.038984645158052444,\n", " -0.04713999107480049,\n", " 0.037763938307762146,\n", " -0.009025425650179386,\n", " 0.0010413328418508172,\n", " 0.0019170913146808743,\n", " -0.006243126932531595,\n", " -0.005408762022852898,\n", " 0.025712722912430763,\n", " -0.00537304999306798,\n", " -0.003035529749467969,\n", " -0.015466592274606228,\n", " 0.008454031310975552,\n", " 0.02119351737201214,\n", " 0.012934278696775436,\n", " -0.013908245600759983,\n", " -0.0012077188584953547,\n", " 0.010298075154423714,\n", " -0.05449019372463226,\n", " 0.014219914563000202,\n", " 0.005532131530344486,\n", " 0.020557193085551262,\n", " 0.014518598094582558,\n", " 0.003181624924764037,\n", " -0.005155530758202076,\n", " -0.006817767396569252,\n", " 0.036023784428834915,\n", " -0.01302518229931593,\n", " 0.030803322792053223,\n", " 0.004999696277081966,\n", " -0.0014390358701348305,\n", " -0.013999149203300476,\n", " 0.02124546281993389,\n", " -0.021531159058213234,\n", " 0.013726438395678997,\n", " -0.01020717155188322,\n", " 0.02603737823665142,\n", " 0.03438752144575119,\n", " 0.014479639939963818,\n", " -0.002085912274196744,\n", " -0.017726195976138115,\n", " -0.001788852270692587,\n", " 0.007532009854912758,\n", " 0.026180226355791092,\n", " 0.017414525151252747,\n", " 0.01601201295852661,\n", " -0.011927846819162369,\n", " -0.023790761828422546,\n", " 0.025530915707349777,\n", " 0.034283630549907684,\n", " 0.017674250528216362,\n", " 0.0390365868806839,\n", " -0.008025486022233963,\n", " -0.0022433700505644083,\n", " -0.022141510620713234,\n", " 0.025115355849266052,\n", " 0.03688087686896324,\n", " -0.018557313829660416,\n", " 0.0002631739480420947,\n", " 0.038153525441884995,\n", " 0.004392590373754501,\n", " -0.01074609998613596,\n", " 0.0059898956678807735,\n", " -0.015115964226424694,\n", " -0.025011466816067696,\n", " 0.024712784215807915,\n", " -0.016609380021691322,\n", " 0.004360124468803406,\n", " 0.004408822860568762,\n", " -0.027686629444360733,\n", " 0.03916645050048828,\n", " -0.020466288551688194,\n", " -0.005509405396878719,\n", " 0.0168561190366745,\n", " 0.02695940062403679,\n", " 0.03264736756682396,\n", " -0.018245644867420197,\n", " 0.019011830911040306,\n", " 0.027141207829117775,\n", " -0.006645699962973595,\n", " -0.02846580184996128,\n", " 0.006622974295169115,\n", " -0.0050483946688473225,\n", " 0.02542702667415142,\n", " -0.001163890352472663,\n", " -0.02390763722360134,\n", " 0.007798227481544018,\n", " 0.0048860665410757065,\n", " -0.00033723600790835917,\n", " 0.01821967214345932,\n", " 0.030777350068092346,\n", " 0.008064445108175278,\n", " -0.006217154674232006,\n", " 0.011797984130680561,\n", " 0.007168395444750786,\n", " -0.022660959511995316,\n", " 0.04155591502785683,\n", " -0.003967291209846735,\n", " -0.005162023939192295,\n", " 0.010116267949342728,\n", " 0.00588275957852602,\n", " 0.035192664712667465,\n", " -0.022725891321897507,\n", " -0.007902117446064949,\n", " -0.0042172763496637344,\n", " -0.00023841895745135844,\n", " 0.013999149203300476,\n", " -0.005509405396878719,\n", " -0.020609136670827866,\n", " 0.017895016819238663,\n", " 0.017726195976138115,\n", " -0.003980277571827173,\n", " 0.037919774651527405,\n", " -0.00521721551194787,\n", " 0.01260313019156456,\n", " 0.01551853772252798,\n", " -0.023855693638324738,\n", " 0.0448024719953537,\n", " -0.025232233107089996,\n", " -0.002777428599074483,\n", " -0.03919242322444916,\n", " 0.002128117484971881,\n", " 0.01995982602238655,\n", " 0.009408518671989441,\n", " -0.009564354084432125,\n", " -0.061087194830179214,\n", " -0.013109592720866203,\n", " 0.021167544648051262,\n", " -0.006915164180099964,\n", " 0.0047334786504507065,\n", " 0.014129011891782284,\n", " 0.023816734552383423,\n", " 0.005132805090397596,\n", " -0.010655196383595467,\n", " -0.019622184336185455,\n", " -0.00819430686533451,\n", " -0.006025607697665691,\n", " 0.0007933771121315658,\n", " -0.0055353776551783085,\n", " -0.02337520197033882,\n", " 0.002459266223013401,\n", " -0.025634806603193283,\n", " 0.0012896943371742964,\n", " 0.007915103808045387,\n", " -0.024803686887025833,\n", " 0.039062559604644775,\n", " 0.005908731836825609,\n", " 0.01169409416615963,\n", " 0.01579124853014946,\n", " -0.008655318059027195,\n", " 0.010414951480925083,\n", " 0.019453363493084908,\n", " -0.005252927541732788,\n", " -0.047945138067007065,\n", " 0.019466349855065346,\n", " -0.012732991948723793,\n", " 0.01729764975607395,\n", " 0.008077431470155716,\n", " -0.008363127708435059,\n", " -0.005334091372787952,\n", " 0.0012945642229169607,\n", " -0.023712843656539917,\n", " 0.0063664959743618965,\n", " -0.006707384716719389,\n", " -0.002769312122836709,\n", " 0.00685023283585906,\n", " 0.0452440045773983,\n", " -0.024180348962545395,\n", " 0.008454031310975552,\n", " -0.01541464775800705,\n", " 0.025621820241212845,\n", " 0.011018810793757439,\n", " -0.009850050322711468,\n", " 0.005187996197491884,\n", " 0.03662114962935448,\n", " 0.028569692745804787,\n", " -0.03394598886370659,\n", " 0.011823956854641438,\n", " 0.016985980793833733,\n", " -0.013739424757659435,\n", " -0.020206565037369728,\n", " 0.011226590722799301,\n", " 0.0040549482218921185,\n", " 0.026543840765953064,\n", " -0.04108841344714165,\n", " -0.015375688672065735,\n", " 0.018570300191640854,\n", " 0.02656981348991394,\n", " -0.03319278731942177,\n", " -0.05184100568294525,\n", " 0.01563541404902935,\n", " -0.008025486022233963,\n", " -0.026167239993810654,\n", " 0.025777654722332954,\n", " -0.0435817651450634,\n", " -0.018765093758702278,\n", " 0.0018278110073879361,\n", " -0.00014254410052672029,\n", " -0.008928028866648674,\n", " 0.011934340000152588,\n", " 0.012564171105623245,\n", " -0.015622426755726337,\n", " 0.0018083316972479224,\n", " 0.02542702667415142,\n", " 0.005577583331614733,\n", " 0.0174664705991745,\n", " -0.01123308390378952,\n", " -0.036854904145002365,\n", " 0.01245378889143467,\n", " 0.011895380914211273,\n", " -0.004587383475154638,\n", " -0.010824017226696014,\n", " 0.013336852192878723,\n", " -0.0055840760469436646,\n", " 0.03477710857987404,\n", " -0.024011528119444847,\n", " 0.04501025006175041,\n", " 0.01920662447810173,\n", " 0.019154680892825127,\n", " 0.021894773468375206,\n", " 0.009090356528759003,\n", " -0.01207718811929226,\n", " -0.022673945873975754,\n", " 0.00248523848131299,\n", " 0.026543840765953064,\n", " 0.011018810793757439,\n", " 0.01625875197350979,\n", " -0.02620619907975197,\n", " -0.005684719420969486,\n", " 0.018648216500878334,\n", " 0.005243187770247459,\n", " -0.025920502841472626,\n", " -0.02580362744629383,\n", " -0.001571333035826683,\n", " -0.020284481346607208,\n", " 0.018700161948800087,\n", " 0.010927907191216946,\n", " -0.023128464818000793,\n", " -0.036543235182762146,\n", " 0.016765214502811432,\n", " -0.00964227132499218,\n", " 0.024167362600564957,\n", " 0.008395593613386154,\n", " -0.033738210797309875,\n", " -0.02072601392865181,\n", " -0.0018554066773504019,\n", " 0.022907698526978493,\n", " 0.026595786213874817,\n", " -0.0007260110578499734,\n", " 0.015661384910345078,\n", " -0.002527443692088127,\n", " 0.020076701417565346,\n", " -0.04552970081567764,\n", " 0.03714060038328171,\n", " 0.007174888625741005,\n", " 0.02218046970665455,\n", " 0.060100242495536804,\n", " -0.02057017758488655,\n", " 0.012025243602693081,\n", " 0.01090193446725607,\n", " 0.03028387390077114,\n", " 0.05124364048242569,\n", " -0.0029608590994030237,\n", " 0.006759329233318567,\n", " -0.015115964226424694,\n", " -0.011486315168440342,\n", " 0.04560761898756027,\n", " 0.010233144275844097,\n", " 0.02929692156612873,\n", " -0.004616602323949337,\n", " 0.015479578636586666,\n", " -0.01958322525024414,\n", " -0.01884301006793976,\n", " 0.0060807992704212666,\n", " 0.01806383766233921,\n", " -0.005538624245673418,\n", " -0.0014861109666526318,\n", " -0.031400687992572784,\n", " 0.009090356528759003,\n", " 0.01989489421248436,\n", " 0.007506037130951881,\n", " -0.007830692455172539,\n", " -0.019336486235260963,\n", " 0.026829538866877556,\n", " -0.022647975012660027,\n", " -0.025712722912430763,\n", " 0.04425705224275589,\n", " 0.00858389399945736,\n", " 0.033452510833740234,\n", " -0.003830936038866639,\n", " -0.0031475359573960304,\n", " 0.035790033638477325,\n", " 0.001391960890032351,\n", " -0.0015916239935904741,\n", " 0.008064445108175278,\n", " -0.0341537669301033,\n", " -0.027998298406600952,\n", " 0.011155166663229465,\n", " -0.001163890352472663,\n", " -0.0018067084020003676,\n", " -0.0176872368901968,\n", " -0.004486740101128817,\n", " -0.021894773468375206,\n", " 0.011453849263489246,\n", " 0.0041458518244326115,\n", " -0.004201043397188187,\n", " 0.0025647792499512434,\n", " 0.018583286553621292,\n", " -0.0021995415445417166,\n", " 0.02027149498462677,\n", " 0.027271069586277008,\n", " -0.0022628495935350657,\n", " 0.022011648863554,\n", " 0.030881240963935852,\n", " 0.002212527906522155,\n", " -0.027063289657235146,\n", " 0.011051276698708534,\n", " 0.00888257659971714,\n", " -0.041711751371622086,\n", " -0.008291703648865223,\n", " -0.015167909674346447,\n", " -0.013044661842286587,\n", " -0.031011102721095085,\n", " 0.002259603003039956,\n", " 0.011570725589990616,\n", " -0.008135869167745113,\n", " 0.024336183443665504,\n", " 0.025245219469070435,\n", " 0.01775216870009899,\n", " -0.005983402486890554,\n", " -0.010375992394983768,\n", " -0.04532191902399063,\n", " -0.03501085937023163,\n", " 0.004639328457415104,\n", " 0.030179984867572784,\n", " 0.02164803445339203,\n", " 0.029972204938530922,\n", " 0.013466713950037956,\n", " 0.009856543503701687,\n", " 0.0006984153296798468,\n", " 0.008408579975366592,\n", " 0.011466835625469685,\n", " -0.02664773166179657,\n", " -0.010421444661915302,\n", " -0.010408458299934864,\n", " 0.03225778043270111,\n", " -0.009005946107208729,\n", " 0.025323135778307915,\n", " 0.033971961587667465,\n", " 0.007174888625741005,\n", " 0.005421748384833336,\n", " 0.01377838384360075,\n", " -0.02088184840977192,\n", " -0.0047529577277600765,\n", " -0.027426904067397118,\n", " 0.0001488342968514189,\n", " -0.023128464818000793,\n", " 0.032153889536857605,\n", " -0.01476533617824316,\n", " 0.02428423799574375,\n", " 0.014284846372902393,\n", " 0.012914799153804779,\n", " 0.020440315827727318,\n", " -0.002861839020624757,\n", " -0.013804355636239052,\n", " 0.03755616024136543,\n", " -0.002384595340117812,\n", " 0.023479092866182327,\n", " -0.012148612178862095,\n", " 0.009876023046672344,\n", " 0.0051587773486971855,\n", " 0.04054298996925354,\n", " 0.018661202862858772,\n", " 0.002676785457879305,\n", " 0.04654262587428093,\n", " 0.03508877754211426,\n", " 0.020063715055584908,\n", " -0.005038654897361994,\n", " -0.024466045200824738,\n", " -0.024660838767886162,\n", " -0.0028163872193545103,\n", " 0.011109714396297932,\n", " -0.002999817719683051,\n", " -0.03358237445354462,\n", " 0.013096606358885765,\n", " 0.02315443754196167,\n", " 0.013752411119639874,\n", " 0.009135808795690536,\n", " -0.004210783168673515,\n", " -0.022037621587514877,\n", " 0.015583468601107597,\n", " -0.015310757793486118,\n", " -0.010785059072077274,\n", " 0.03646531701087952,\n", " 0.012888827361166477,\n", " -0.012739485129714012,\n", " 0.0022157744970172644,\n", " -0.0013700466370210052,\n", " -0.03153055161237717,\n", " 0.010363006964325905,\n", " 0.009713695384562016,\n", " 0.008876084350049496,\n", " -0.010447417385876179,\n", " -0.025414040312170982,\n", " 0.039893679320812225,\n", " 0.0003485989582259208,\n", " 0.011168152093887329,\n", " -0.009603312239050865,\n", " -0.0010437677847221494,\n", " -0.01259663701057434,\n", " -0.005843800492584705,\n", " -0.025401053950190544,\n", " -0.0011452225735411048,\n", " 0.012687540613114834,\n", " -0.01043443102389574,\n", " 0.0007787676295265555,\n", " -0.002551792887970805,\n", " 0.00645415298640728,\n", " -0.021362338215112686,\n", " 0.023712843656539917,\n", " -0.00805145874619484,\n", " -0.012875840999186039,\n", " -0.013278413563966751,\n", " 0.004197796806693077,\n", " 0.007928089238703251,\n", " 0.017648277804255486,\n", " -0.014518598094582558,\n", " 0.028595665469765663,\n", " -0.002326157409697771,\n", " 0.0007389973034150898,\n", " 0.01944037713110447,\n", " -0.025920502841472626,\n", " 0.008707262575626373,\n", " 0.01407706644386053,\n", " 0.00521721551194787,\n", " 0.01859627291560173,\n", " 0.01836252026259899,\n", " -0.00736318901181221,\n", " 0.0036848410964012146,\n", " 0.02567376382648945,\n", " -0.007480064872652292,\n", " -0.027063289657235146,\n", " -0.031712356954813004,\n", " -0.0016476270975545049,\n", " -0.019635170698165894,\n", " -0.008142362348735332,\n", " -0.014622488059103489,\n", " -0.009869529865682125,\n", " -0.00045654692803509533,\n", " 0.00015654486196581274,\n", " -0.011018810793757439,\n", " 0.003201104234904051,\n", " 0.000650528643745929,\n", " ...]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["text=\"This is a tutorial on OPENAI embeddings\"\n", "query_result=embeddings.embed_query(text)\n", "query_result"]}, {"cell_type": "code", "execution_count": 8, "id": "9aa9c943", "metadata": {}, "outputs": [{"data": {"text/plain": ["3072"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["len(query_result)"]}, {"cell_type": "code", "execution_count": 9, "id": "3952cd41", "metadata": {}, "outputs": [{"data": {"text/plain": ["[0.00669329846277833,\n", " 0.0330885611474514,\n", " -0.005008494481444359,\n", " -0.027267685160040855,\n", " 0.015272730030119419,\n", " 0.008759567514061928,\n", " -0.0007465941598638892,\n", " 0.04020924121141434,\n", " -0.025755954906344414,\n", " -0.0011399800423532724,\n", " 0.007466824725270271,\n", " -0.013153479434549809,\n", " 0.005294593516737223,\n", " -0.03396451845765114,\n", " 0.007572787348181009,\n", " 0.014481542631983757,\n", " 0.03582945838570595,\n", " 0.045464981347322464,\n", " -0.04224371910095215,\n", " -0.019638385623693466,\n", " 4.845577132073231e-05,\n", " 0.009388278238475323,\n", " -0.008929107338190079,\n", " -0.009338829666376114,\n", " 0.009508369490504265,\n", " 0.0005880919052287936,\n", " 0.00696526886895299,\n", " 0.03803347796201706,\n", " -0.03503827005624771,\n", " 0.028567492961883545,\n", " 0.02664603851735592,\n", " -0.001981498906388879,\n", " 0.0265471413731575,\n", " -0.012122111395001411,\n", " -0.010589187033474445,\n", " -0.002226978773251176,\n", " 0.04908183589577675,\n", " 0.004623497370630503,\n", " -0.05524178966879845,\n", " 0.023099826648831367,\n", " 0.03209957480430603,\n", " -0.0030446562450379133,\n", " -0.009642588905990124,\n", " -0.007339669857174158,\n", " -0.034105800092220306,\n", " -0.03144967183470726,\n", " -0.02447027526795864,\n", " -0.02499302476644516,\n", " -0.00672508729621768,\n", " -0.029810786247253418,\n", " 0.026561269536614418,\n", " 0.0035656385589390993,\n", " 0.01719418354332447,\n", " 0.00033753475872799754,\n", " -0.019172150641679764,\n", " 0.022294512018561363,\n", " 0.03594248369336128,\n", " 0.00879488792270422,\n", " 0.045323695987463,\n", " 0.010122952051460743,\n", " -0.002928097266703844,\n", " 0.0011682367185130715,\n", " -0.009741486981511116,\n", " -0.019511230289936066,\n", " 0.02425835095345974,\n", " -0.014248425140976906,\n", " 0.002255235332995653,\n", " -0.01044083945453167,\n", " -0.0034879327286034822,\n", " 0.04648222029209137,\n", " 0.020584983751177788,\n", " -0.012425870634615421,\n", " -0.02728181518614292,\n", " -0.013803383335471153,\n", " 0.0009174587321467698,\n", " 0.005347574595361948,\n", " 0.04258280247449875,\n", " 0.01689748838543892,\n", " -0.029895557090640068,\n", " 0.03447313606739044,\n", " 0.021192502230405807,\n", " -0.015060804784297943,\n", " 0.030743256211280823,\n", " 0.03687495365738869,\n", " 0.06007367745041847,\n", " 0.001811958965845406,\n", " 0.0232552383095026,\n", " -0.06821160018444061,\n", " 0.002758557442575693,\n", " -0.0023011525627225637,\n", " -0.020669754594564438,\n", " 0.027352456003427505,\n", " -0.03554689139127731,\n", " -0.007622236385941505,\n", " -0.01109074242413044,\n", " -0.012567154131829739,\n", " 0.041819870471954346,\n", " -0.009826256893575191,\n", " -0.015527039766311646,\n", " 0.03260819613933563,\n", " 0.01277907844632864,\n", " -0.010469095781445503,\n", " 0.000883903936482966,\n", " -0.022336896508932114,\n", " -0.0012353463098406792,\n", " 0.003145320573821664,\n", " -0.015145574696362019,\n", " 0.01007350254803896,\n", " 0.012913297861814499,\n", " 0.017476750537753105,\n", " 0.01024304237216711,\n", " 0.008088471367955208,\n", " 0.0029210331849753857,\n", " 0.04837541654706001,\n", " -0.013033389113843441,\n", " -0.013393661007285118,\n", " -0.0265047550201416,\n", " -0.03469919040799141,\n", " -0.015399884432554245,\n", " -0.02893482893705368,\n", " 0.003367841709405184,\n", " -0.03260819613933563,\n", " -0.005623077042400837,\n", " 0.006212935317307711,\n", " 0.007382054813206196,\n", " 0.046199653297662735,\n", " -0.036281563341617584,\n", " -0.008116728626191616,\n", " 0.02872290462255478,\n", " 0.018550503998994827,\n", " 0.024314863607287407,\n", " 0.018607016652822495,\n", " -0.007081827614456415,\n", " 0.02356606163084507,\n", " 0.03088453970849514,\n", " 0.020302416756749153,\n", " 0.002173997461795807,\n", " 0.02256295084953308,\n", " 0.0227042343467474,\n", " 0.045804060995578766,\n", " -0.02300092950463295,\n", " -0.01880481280386448,\n", " -0.0028080064803361893,\n", " 0.00621646735817194,\n", " 0.009571947157382965,\n", " -0.024244222790002823,\n", " -0.04193289577960968,\n", " -0.0023523676209151745,\n", " -0.016911616548895836,\n", " 0.005753764417022467,\n", " -0.04622790962457657,\n", " -0.021022962406277657,\n", " 0.0030163994524627924,\n", " 0.03153444454073906,\n", " 0.02018938958644867,\n", " -0.00012925219198223203,\n", " 0.012199817225337029,\n", " 0.016388868913054466,\n", " 0.006831049919128418,\n", " -0.0014737618621438742,\n", " 0.02421596646308899,\n", " -0.023891013115644455,\n", " 0.009649652987718582,\n", " 0.0194547176361084,\n", " 0.0333428718149662,\n", " -0.00851232185959816,\n", " 0.0036062574945390224,\n", " -0.010320748202502728,\n", " -0.05238786339759827,\n", " 0.025967879220843315,\n", " -0.008159113116562366,\n", " 0.03495350107550621,\n", " 0.0709807500243187,\n", " 0.02100883424282074,\n", " -0.062221184372901917,\n", " 0.00022804015316069126,\n", " 0.010652764700353146,\n", " 0.0200057215988636,\n", " -0.01230577938258648,\n", " 0.004778909031301737,\n", " -0.0326647087931633,\n", " 0.0036203856579959393,\n", " 0.0016945170937106013,\n", " 0.010688085108995438,\n", " 0.008434616029262543,\n", " -0.005312253721058369,\n", " 0.03698797896504402,\n", " -0.010497353039681911,\n", " 0.03568817302584648,\n", " -0.02369321696460247,\n", " 0.025586415082216263,\n", " -0.0027550251688808203,\n", " -0.007124212570488453,\n", " -0.004627029411494732,\n", " 0.04374132305383682,\n", " -0.015272730030119419,\n", " 0.007954252883791924,\n", " 0.035320837050676346,\n", " 0.04430645704269409,\n", " -0.012319907546043396,\n", " 0.0058279382064938545,\n", " -0.07844051718711853,\n", " 0.0097273588180542,\n", " -0.03269296512007713,\n", " -0.011013036593794823,\n", " 0.05168144777417183,\n", " -3.9377213397528976e-05,\n", " 0.00666150962933898,\n", " -0.007982509210705757,\n", " -0.024851741269230843,\n", " 0.004754184745252132,\n", " -0.014863007701933384,\n", " -0.034981757402420044,\n", " -0.01936994679272175,\n", " 0.013217057101428509,\n", " 0.00905626267194748,\n", " -0.040435295552015305,\n", " 0.014636955223977566,\n", " -0.022153228521347046,\n", " -0.0014093014178797603,\n", " -0.009755615144968033,\n", " 0.04829064756631851,\n", " 0.06182559207081795,\n", " -0.019652513787150383,\n", " -0.03687495365738869,\n", " 0.0007037676987238228,\n", " -0.007382054813206196,\n", " -0.023961655795574188,\n", " -0.006668574176728725,\n", " 0.01133092399686575,\n", " 0.03048894554376602,\n", " 0.02824254147708416,\n", " 0.035716429352760315,\n", " 0.0020927595905959606,\n", " -0.0010163570987060666,\n", " -0.011196705512702465,\n", " 0.03348415344953537,\n", " -0.013471366837620735,\n", " -0.006318897474557161,\n", " 0.04326096177101135,\n", " -1.8915963664767332e-05,\n", " 0.029076112434267998,\n", " -0.0100028607994318,\n", " -0.0460018590092659,\n", " -0.005527711007744074,\n", " 0.04911009222269058,\n", " 0.0013722145231440663,\n", " 0.03885291889309883,\n", " -0.022082587704062462,\n", " -0.0015029016649350524,\n", " 0.006082247942686081,\n", " 0.024201838299632072,\n", " 0.03885291889309883,\n", " 0.021418554708361626,\n", " -0.02681558020412922,\n", " 0.0521618127822876,\n", " 0.028963085263967514,\n", " -0.02247818000614643,\n", " -0.004259692970663309,\n", " -0.00723370723426342,\n", " -0.006573207676410675,\n", " 0.027959974482655525,\n", " 0.028525108471512794,\n", " 0.0072690281085669994,\n", " -0.020599111914634705,\n", " -0.04097216948866844,\n", " 0.0232552383095026,\n", " -0.022040201351046562,\n", " 0.00615288969129324,\n", " 0.021630480885505676,\n", " 0.019553614780306816,\n", " -0.03885291889309883,\n", " 0.06917233020067215,\n", " 0.02646237052977085,\n", " 0.022294512018561363,\n", " -0.0324951708316803,\n", " -0.006799261085689068,\n", " -0.0064389887265861034,\n", " 0.041339509189128876,\n", " -0.002732066670432687,\n", " 0.05422454699873924,\n", " -0.013365404680371284,\n", " 0.026052650064229965,\n", " 0.014262554235756397,\n", " -0.013301827013492584,\n", " -0.01576722227036953,\n", " -0.014460350386798382,\n", " 0.03105407953262329,\n", " -0.05651333928108215,\n", " -0.016304098069667816,\n", " -0.01163468323647976,\n", " -0.013181736692786217,\n", " 0.020683882758021355,\n", " 0.04687781631946564,\n", " -0.0033289887942373753,\n", " 0.008363974280655384,\n", " -0.009550753980875015,\n", " -0.009494241327047348,\n", " -0.01612043008208275,\n", " -0.0230433139950037,\n", " 0.0057678925804793835,\n", " 0.0166290495544672,\n", " -0.04631268233060837,\n", " -0.039305027574300766,\n", " 0.002608443843200803,\n", " 0.049618709832429886,\n", " -0.0054323445074260235,\n", " 0.015329243615269661,\n", " -0.047669000923633575,\n", " 0.001433142926543951,\n", " 0.03549037501215935,\n", " -0.014749981462955475,\n", " -0.01681271754205227,\n", " -0.02434311993420124,\n", " -0.00827214028686285,\n", " -0.0011832480086013675,\n", " 0.04388260841369629,\n", " -0.0015002525178715587,\n", " -0.01862114481627941,\n", " -0.015470526181161404,\n", " 0.048488445580005646,\n", " -0.01589437574148178,\n", " -0.025487516075372696,\n", " 0.02153158187866211,\n", " 0.024710457772016525,\n", " -0.011076614260673523,\n", " 0.03780742362141609,\n", " 0.007374990731477737,\n", " 0.007784712594002485,\n", " -0.004655286204069853,\n", " -0.02465394325554371,\n", " -0.030178122222423553,\n", " -0.005877387244254351,\n", " -0.011945506557822227,\n", " 0.02083929441869259,\n", " 0.0039029524195939302,\n", " 0.007367926649749279,\n", " 0.009776807390153408,\n", " -0.0046800109557807446,\n", " 0.004775376990437508,\n", " -0.004499874543398619,\n", " -0.04275234043598175,\n", " -0.018592888489365578,\n", " -0.017773445695638657,\n", " -0.007473889272660017,\n", " -0.016558408737182617,\n", " 0.058321766555309296,\n", " 0.052698686718940735,\n", " -0.012009084224700928,\n", " 0.005103860981762409,\n", " 0.0008980322745628655,\n", " 0.019129766151309013,\n", " 0.01531511452049017,\n", " -0.023113954812288284,\n", " -0.0063047693111002445,\n", " -0.04588882997632027,\n", " -0.014905393123626709,\n", " -0.005283997394144535,\n", " -0.026052650064229965,\n", " 0.022124972194433212,\n", " 0.001237995340488851,\n", " 0.008420487865805626,\n", " -0.003148852614685893,\n", " 0.0026561268605291843,\n", " 0.015583553351461887,\n", " -0.054252807050943375,\n", " -0.021249014884233475,\n", " -0.029867298901081085,\n", " 0.027182916179299355,\n", " 0.007502145599573851,\n", " -0.013979987241327763,\n", " -0.003906484693288803,\n", " -0.024837613105773926,\n", " -0.004778909031301737,\n", " -0.023099826648831367,\n", " -0.03676192834973335,\n", " -0.014947778545320034,\n", " -0.025883108377456665,\n", " 0.020655624568462372,\n", " 0.0454932376742363,\n", " 0.016219329088926315,\n", " -0.004980238154530525,\n", " 0.035631660372018814,\n", " 0.014050628989934921,\n", " 0.01836683601140976,\n", " 0.013739805668592453,\n", " -0.008809017017483711,\n", " -0.010179465636610985,\n", " -0.019115636125206947,\n", " 0.015908505767583847,\n", " 0.004323270637542009,\n", " -0.05397024005651474,\n", " 0.016473637893795967,\n", " -0.015371628105640411,\n", " 0.04331747442483902,\n", " 0.015880247578024864,\n", " -0.01593676209449768,\n", " 0.003436717437580228,\n", " 0.029160883277654648,\n", " 0.03136490285396576,\n", " -0.0456627793610096,\n", " 0.000604869332164526,\n", " -0.015470526181161404,\n", " 0.0003483517502900213,\n", " -0.00935295782983303,\n", " -0.008766631595790386,\n", " -0.050664208829402924,\n", " -0.02577008306980133,\n", " 0.035462118685245514,\n", " 0.031817011535167694,\n", " 0.0020486086141318083,\n", " -0.017293082550168037,\n", " -0.02949996292591095,\n", " 0.023961655795574188,\n", " 0.004196115303784609,\n", " 0.028426209464669228,\n", " -0.01727895252406597,\n", " 0.024964766576886177,\n", " -0.027832819148898125,\n", " -0.007509209681302309,\n", " 0.0026914477348327637,\n", " -0.027324199676513672,\n", " 0.027974102646112442,\n", " 0.03184526786208153,\n", " -0.04453251138329506,\n", " 0.021969560533761978,\n", " 0.01054680161178112,\n", " 0.018084269016981125,\n", " -0.028652261942625046,\n", " -0.03582945838570595,\n", " -0.044108662754297256,\n", " 0.0009607267566025257,\n", " 0.01996333710849285,\n", " 0.04679304361343384,\n", " -0.05320730805397034,\n", " -0.01037726178765297,\n", " -0.004846018739044666,\n", " 0.007544530555605888,\n", " -0.010419647209346294,\n", " 0.02404642663896084,\n", " -0.041311249136924744,\n", " -0.02927391044795513,\n", " 0.006767472252249718,\n", " -0.008116728626191616,\n", " -0.06352099031209946,\n", " 0.017180055379867554,\n", " -0.010730470530688763,\n", " -0.028609877452254295,\n", " 0.04032226651906967,\n", " -0.045154158025979996,\n", " -0.011337989009916782,\n", " 0.01658666506409645,\n", " -0.0730152353644371,\n", " -0.061656054109334946,\n", " 0.02321285381913185,\n", " -0.023664960637688637,\n", " -0.004457489587366581,\n", " 0.045634523034095764,\n", " -0.007671685889363289,\n", " -0.024159451946616173,\n", " -0.007148937322199345,\n", " -0.007933059707283974,\n", " 0.03472744673490524,\n", " 0.006276512518525124,\n", " 0.022492308169603348,\n", " -0.026561269536614418,\n", " -0.00250071519985795,\n", " -0.04227197915315628,\n", " 0.005563031882047653,\n", " 0.019553614780306816,\n", " -0.030828027054667473,\n", " -0.020161133259534836,\n", " 0.05738929659128189,\n", " 0.04453251138329506,\n", " -0.01355613674968481,\n", " -0.026391729712486267,\n", " -0.019468845799565315,\n", " -0.004581112414598465,\n", " -0.02918913960456848,\n", " -0.010228914208710194,\n", " 0.018070140853524208,\n", " 0.05238786339759827,\n", " -0.007834161631762981,\n", " 0.003341351170092821,\n", " -0.003295434173196554,\n", " 0.01531511452049017,\n", " 0.03436011075973511,\n", " 0.0036309820134192705,\n", " -0.013831639662384987,\n", " 0.020457828417420387,\n", " -0.013443110510706902,\n", " 0.012143303640186787,\n", " -0.005294593516737223,\n", " -0.062221184372901917,\n", " -0.006467245053499937,\n", " -0.01702464371919632,\n", " -0.03676192834973335,\n", " 0.015060804784297943,\n", " 0.020076364278793335,\n", " 0.02083929441869259,\n", " -0.03936154022812843,\n", " -0.0038358429446816444,\n", " -0.004252628888934851,\n", " 0.00327600771561265,\n", " 0.017406107857823372,\n", " 0.0055206469260156155,\n", " -0.004379783757030964,\n", " -0.00011942857963731512,\n", " -0.009232866577804089,\n", " 0.0232552383095026,\n", " 0.02195543237030506,\n", " -0.003198301885277033,\n", " 0.018522247672080994,\n", " 0.009367085993289948,\n", " 0.012870913371443748,\n", " 0.014410901814699173,\n", " 0.03724228963255882,\n", " 0.025445131585001945,\n", " 0.02044370025396347,\n", " -0.010433775372803211,\n", " -0.0018490458605811,\n", " 0.025190820917487144,\n", " 0.011253218166530132,\n", " 0.0009792702039703727,\n", " -0.03760962560772896,\n", " 0.03399277478456497,\n", " 0.02780456282198429,\n", " -0.0526704303920269,\n", " -0.03549037501215935,\n", " -0.026660168543457985,\n", " 0.001185014029033482,\n", " -0.019892694428563118,\n", " -0.002168699400499463,\n", " -0.03272122144699097,\n", " 0.010984780266880989,\n", " -0.008462872356176376,\n", " 0.021065346896648407,\n", " 0.017872342839837074,\n", " -0.005725507624447346,\n", " -0.014354388229548931,\n", " -0.01338659692555666,\n", " 0.019158022478222847,\n", " -0.009317636489868164,\n", " 0.015823734924197197,\n", " -0.012164495885372162,\n", " 0.013838703744113445,\n", " -0.003977126441895962,\n", " -0.04422168806195259,\n", " -0.0389942042529583,\n", " 0.0531790517270565,\n", " -0.0026296363212168217,\n", " -0.010935330763459206,\n", " 0.012581282295286655,\n", " -0.0017642758321017027,\n", " 0.012991003692150116,\n", " 0.0262928307056427,\n", " -0.019822053611278534,\n", " 0.002230510814115405,\n", " -0.03226911649107933,\n", " 0.0001115917693823576,\n", " -0.00366630288772285,\n", " 0.012454126961529255,\n", " -0.015216216444969177,\n", " 0.0114156948402524,\n", " 0.016007402911782265,\n", " -0.026095034554600716,\n", " 0.008194434456527233,\n", " 0.036281563341617584,\n", " -0.0008680095779709518,\n", " 0.030912796035408974,\n", " 0.029160883277654648,\n", " -0.02209671586751938,\n", " -0.00709242420271039,\n", " 0.01815490983426571,\n", " -0.007629300933331251,\n", " 0.026349343359470367,\n", " 0.014071821235120296,\n", " -0.009239930659532547,\n", " -0.031082335859537125,\n", " 0.0014437391655519605,\n", " -0.0074244397692382336,\n", " 0.013725677505135536,\n", " -0.006336558144539595,\n", " 0.01633235439658165,\n", " 0.02581246756017208,\n", " 0.003101169364526868,\n", " -0.0020397782791405916,\n", " -0.0147923668846488,\n", " -0.0023717940784990788,\n", " -0.005351106636226177,\n", " 0.0266742967069149,\n", " 0.02027416042983532,\n", " 0.02767740748822689,\n", " -0.00037484237691387534,\n", " -0.024371378123760223,\n", " -0.0046800109557807446,\n", " 0.021192502230405807,\n", " 0.0296695027500391,\n", " 0.03854209557175636,\n", " -0.0074526965618133545,\n", " -0.02512017823755741,\n", " -0.01754739135503769,\n", " 0.018225552514195442,\n", " 0.021234886720776558,\n", " -0.02585485205054283,\n", " -0.00801782961934805,\n", " 0.04114171117544174,\n", " -0.004358591046184301,\n", " 0.007180726155638695,\n", " 0.004369187634438276,\n", " -0.02975427359342575,\n", " -0.004715331830084324,\n", " 0.012044405564665794,\n", " -0.005379363428801298,\n", " 0.007890675216913223,\n", " 0.010695149190723896,\n", " -0.022054331377148628,\n", " 0.03789219260215759,\n", " -0.002694979775696993,\n", " 0.04255454242229462,\n", " 0.02356606163084507,\n", " 0.02599613554775715,\n", " 0.0033925664611160755,\n", " -0.00861828401684761,\n", " 0.011973763816058636,\n", " 0.022365154698491096,\n", " -0.008060215041041374,\n", " -0.010363133624196053,\n", " -0.010327812284231186,\n", " 0.005365235265344381,\n", " 0.025798339396715164,\n", " -0.004121941514313221,\n", " -0.01429081056267023,\n", " 0.006029266864061356,\n", " 0.016176942735910416,\n", " -0.00018377872765995562,\n", " -0.004432764835655689,\n", " -0.0005646919016726315,\n", " 0.02625044621527195,\n", " -0.0064389887265861034,\n", " 0.035066526383161545,\n", " 0.01802775450050831,\n", " -0.007445632480084896,\n", " 0.043684810400009155,\n", " -0.020669754594564438,\n", " -0.007544530555605888,\n", " 0.02222387120127678,\n", " 0.019949208945035934,\n", " 0.022280383855104446,\n", " -0.034021031111478806,\n", " 0.04097216948866844,\n", " -0.01840922050178051,\n", " -0.00911984033882618,\n", " 0.016727948561310768,\n", " -0.01585199125111103,\n", " -0.004001850727945566,\n", " 0.0013633843045681715,\n", " 0.0026455307379364967,\n", " -0.015668323263525963,\n", " 0.02157396636903286,\n", " 4.1474388126516715e-05,\n", " 0.015117318369448185,\n", " -0.005658397916704416,\n", " -0.014806495048105717,\n", " 0.023382393643260002,\n", " -0.02447027526795864,\n", " -0.012334036640822887,\n", " -0.018818940967321396,\n", " -0.010250106453895569,\n", " 0.03712926432490349,\n", " 0.01606391742825508,\n", " -0.016530152410268784,\n", " -0.044165175408124924,\n", " -0.026660168543457985,\n", " -0.011684132739901543,\n", " 0.001250357599928975,\n", " -0.005481794010847807,\n", " 0.008505257777869701,\n", " 0.02876528911292553,\n", " 0.0014419731451198459,\n", " -0.018663529306650162,\n", " -0.017942985519766808,\n", " 0.004817761946469545,\n", " -0.021800020709633827,\n", " -0.0009227568516507745,\n", " -0.004881339613348246,\n", " -0.007382054813206196,\n", " 0.030206380411982536,\n", " -0.013633842580020428,\n", " 0.00112320261541754,\n", " -0.010892946273088455,\n", " -0.018875455483794212,\n", " 0.024060554802417755,\n", " 0.010334877297282219,\n", " 0.014389708638191223,\n", " -0.0008741907076910138,\n", " 0.01719418354332447,\n", " -0.013831639662384987,\n", " 0.014029436744749546,\n", " -0.017208311706781387,\n", " -0.027733920142054558,\n", " 0.024399634450674057,\n", " -0.007897739298641682,\n", " 0.02113598957657814,\n", " 0.02742309682071209,\n", " 0.004651754163205624,\n", " 0.009487176313996315,\n", " 0.02876528911292553,\n", " -0.011804223991930485,\n", " -0.0033607776276767254,\n", " -0.012701372615993023,\n", " -0.0075586591847240925,\n", " 0.0009139266330748796,\n", " 0.04990127682685852,\n", " -0.01381751149892807,\n", " 0.01163468323647976,\n", " -0.015527039766311646,\n", " -2.1289082724251784e-05,\n", " 0.011203769594430923,\n", " 0.012722565792500973,\n", " -0.010998908430337906,\n", " 0.03153444454073906,\n", " 0.021065346896648407,\n", " -0.036846697330474854,\n", " 0.0035903630778193474,\n", " 0.01429081056267023,\n", " 0.013895217329263687,\n", " -0.020302416756749153,\n", " 0.003744008718058467,\n", " -9.911909728543833e-05,\n", " -0.004506938625127077,\n", " -0.030573716387152672,\n", " 0.011253218166530132,\n", " 0.008378102444112301,\n", " 0.01944058947265148,\n", " -0.044419486075639725,\n", " -0.010045246221125126,\n", " 0.012553025037050247,\n", " -0.03390800207853317,\n", " -0.0033289887942373753,\n", " 0.022336896508932114,\n", " -0.0459170863032341,\n", " -0.0293304231017828,\n", " -0.004920192528516054,\n", " -0.00018775231728795916,\n", " -0.02486586943268776,\n", " 0.022492308169603348,\n", " 0.0011673537082970142,\n", " -0.01828206516802311,\n", " 0.016699692234396935,\n", " 0.030686743557453156,\n", " -0.023679088801145554,\n", " 0.03040417656302452,\n", " -0.009713229723274708,\n", " -0.04877101257443428,\n", " 0.02918913960456848,\n", " 0.01836683601140976,\n", " -0.006446052808314562,\n", " -0.012107983231544495,\n", " 0.014523928053677082,\n", " 0.000834454782307148,\n", " 0.04682129994034767,\n", " -0.016869232058525085,\n", " 0.04114171117544174,\n", " 0.03885291889309883,\n", " -0.025360360741615295,\n", " -0.0009536626166664064,\n", " 0.010031118057668209,\n", " -0.0038393749855458736,\n", " -0.04447599872946739,\n", " 0.006898159626871347,\n", " -0.0028645198326557875,\n", " 0.020556727424263954,\n", " 0.008992685005068779,\n", " -0.010553865693509579,\n", " 0.0024742246605455875,\n", " 0.005856194533407688,\n", " -0.009734422899782658,\n", " -0.0328342504799366,\n", " -0.021757636219263077,\n", " 0.00024106471391860396,\n", " -0.011599362827837467,\n", " -0.0010340175358578563,\n", " 0.017434364184737206,\n", " -0.023622576147317886,\n", " -0.022294512018561363,\n", " 0.02201194502413273,\n", " -0.008964428678154945,\n", " 0.029528219252824783,\n", " 0.008604155853390694,\n", " -0.026052650064229965,\n", " -0.0006326845032162964,\n", " -0.006297705229371786,\n", " 0.017391979694366455,\n", " -0.0010198891395702958,\n", " 0.015611809678375721,\n", " 0.007074763532727957,\n", " 0.012510640546679497,\n", " -0.003853503381833434,\n", " -0.03517955169081688,\n", " 0.01719418354332447,\n", " 0.009473048150539398,\n", " 0.015738964080810547,\n", " 0.026829708367586136,\n", " -0.025275589898228645,\n", " 0.013188800774514675,\n", " 0.008053150959312916,\n", " -5.861051249667071e-05,\n", " 0.04521067067980766,\n", " -0.018253808841109276,\n", " 0.000905979482922703,\n", " 0.0012618368491530418,\n", " -0.01020065788179636,\n", " 0.014523928053677082,\n", " 0.027917589992284775,\n", " 0.009190482087433338,\n", " -0.016614921391010284,\n", " -0.01243293471634388,\n", " -0.039135485887527466,\n", " -0.0325799398124218,\n", " 0.010963588021695614,\n", " -0.0073961834423244,\n", " 0.00037925748620182276,\n", " 0.0042208400554955006,\n", " -0.009988732635974884,\n", " 0.024074682965874672,\n", " 0.029641246423125267,\n", " 0.014566313475370407,\n", " 0.00015419752162415534,\n", " -0.031647469848394394,\n", " -0.0015867885667830706,\n", " -0.019906824454665184,\n", " -0.03063022904098034,\n", " 0.017618034034967422,\n", " -0.0034596759360283613,\n", " 0.03509478271007538,\n", " 0.0037369446363300085,\n", " -0.014100078493356705,\n", " 0.035716429352760315,\n", " -0.006311833392828703,\n", " -0.008695989847183228,\n", " -0.010702213272452354,\n", " -0.014093013480305672,\n", " -0.014509799890220165,\n", " -0.03503827005624771,\n", " 0.007367926649749279,\n", " -0.01247531920671463,\n", " -0.016600793227553368,\n", " 0.012199817225337029,\n", " -0.03574468567967415,\n", " 0.029160883277654648,\n", " 0.018267937004566193,\n", " -0.013280634768307209,\n", " 0.0006375411176122725,\n", " 0.008999749086797237,\n", " -0.013499624095857143,\n", " 0.03226911649107933,\n", " 0.0166290495544672,\n", " 0.021630480885505676,\n", " 8.201057062251493e-05,\n", " 0.04218720644712448,\n", " 0.02182827703654766,\n", " -0.04653873294591904,\n", " 0.017787573859095573,\n", " 0.021319657564163208,\n", " -0.03746834397315979,\n", " 0.0019179214723408222,\n", " -0.016954001039266586,\n", " 0.0019020270556211472,\n", " -0.034331854432821274,\n", " -0.004457489587366581,\n", " -0.0033890341874212027,\n", " 0.001963838469237089,\n", " 0.021997816860675812,\n", " 0.03574468567967415,\n", " 0.0387398935854435,\n", " 0.013259442523121834,\n", " -0.008773695677518845,\n", " -0.04631268233060837,\n", " -0.017688674852252007,\n", " 0.017688674852252007,\n", " 0.03591422736644745,\n", " -0.0003819065459538251,\n", " 0.023156341165304184,\n", " 0.033512409776449203,\n", " 0.0026755533181130886,\n", " -0.0020026913844048977,\n", " 0.020260032266378403,\n", " 0.038005221635103226,\n", " -0.014410901814699173,\n", " 0.0038852919824421406,\n", " -0.0017298379680141807,\n", " 0.040350522845983505,\n", " -0.017180055379867554,\n", " 0.03289076313376427,\n", " 0.0058738552033901215,\n", " -0.026151547208428383,\n", " -0.003525019623339176,\n", " -0.013379532843828201,\n", " -0.009812128730118275,\n", " -0.005835002288222313,\n", " -0.03153444454073906,\n", " 0.0023594319354742765,\n", " -0.006898159626871347,\n", " 0.03724228963255882,\n", " -0.02755025215446949,\n", " 0.02637760154902935,\n", " -0.004506938625127077,\n", " 0.025190820917487144,\n", " 0.0132523775100708,\n", " -0.007615172304213047,\n", " -0.0016044490039348602,\n", " 0.0197937972843647,\n", " 0.0011594064999371767,\n", " 0.01797124184668064,\n", " -0.005093264859169722,\n", " -0.0022764280438423157,\n", " 0.006587335839867592,\n", " 0.02400404028594494,\n", " -0.006018670741468668,\n", " -0.0033978645224124193,\n", " 0.03797696530818939,\n", " 0.014312002807855606,\n", " 0.002747961087152362,\n", " -0.0013889919500797987,\n", " -0.026744937524199486,\n", " -0.04569103568792343,\n", " -0.009416535496711731,\n", " 0.0037263482809066772,\n", " -0.008745439350605011,\n", " -0.019002610817551613,\n", " 0.001734253135509789,\n", " 0.009246994741261005,\n", " 0.020302416756749153,\n", " -0.000876398291438818,\n", " -0.013287698850035667,\n", " -0.02295854315161705,\n", " 0.02071213908493519,\n", " -0.017533263191580772,\n", " -0.00868892576545477,\n", " 0.0198785662651062,\n", " 0.03079976886510849,\n", " -0.0050155590288341045,\n", " 0.010278363712131977,\n", " 0.016996387392282486,\n", " -0.03390800207853317,\n", " 0.02560054324567318,\n", " 0.002566058887168765,\n", " -0.007565723266452551,\n", " -0.01357732992619276,\n", " -0.03447313606739044,\n", " -0.002181061776354909,\n", " 0.005135649815201759,\n", " 0.012115047313272953,\n", " -0.0014384411042556167,\n", " 0.0015673621091991663,\n", " -0.026575397700071335,\n", " -0.007367926649749279,\n", " 0.0029298635199666023,\n", " 0.00353031768463552,\n", " -0.009783871471881866,\n", " 0.006721555255353451,\n", " 0.0069158198311924934,\n", " -0.007339669857174158,\n", " -0.00452459929510951,\n", " -0.007869482040405273,\n", " -0.0020786311943084,\n", " -0.007480953354388475,\n", " -0.011083678342401981,\n", " -0.022591207176446915,\n", " -0.0073467339389026165,\n", " 0.012694308534264565,\n", " 0.024244222790002823,\n", " -0.03119536302983761,\n", " 0.033201586455106735,\n", " -0.007011186331510544,\n", " 0.0017792871221899986,\n", " 0.02130552940070629,\n", " -0.0007532168528996408,\n", " -0.005693718791007996,\n", " 0.011451015248894691,\n", " -0.015046676620841026,\n", " 0.00879488792270422,\n", " 0.026872092857956886,\n", " 0.006880498956888914,\n", " 0.012326971627771854,\n", " 0.027126403525471687,\n", " -0.008837273344397545,\n", " -0.04037877917289734,\n", " -0.018734171986579895,\n", " -0.01819729432463646,\n", " 0.011881929822266102,\n", " -0.01459456980228424,\n", " -0.02274661883711815,\n", " -0.012609538622200489,\n", " 0.01815490983426571,\n", " 0.017010515555739403,\n", " 0.0004724161990452558,\n", " -0.008095535449683666,\n", " -0.001910857274197042,\n", " ...]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["text=\"This is a tutorial on OPENAI embeddings. My name is <PERSON><PERSON>\"\n", "query_result=embeddings.embed_query(text)\n", "query_result"]}, {"cell_type": "code", "execution_count": 11, "id": "d3bc48ae", "metadata": {}, "outputs": [{"data": {"text/plain": ["3072"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["len(query_result)"]}, {"cell_type": "code", "execution_count": 12, "id": "07aa0a07", "metadata": {}, "outputs": [], "source": ["from langchain_openai import OpenAIEmbeddings\n", "\n", "embeddings = OpenAIEmbeddings(model=\"text-embedding-3-large\",dimensions=1024)"]}, {"cell_type": "code", "execution_count": 14, "id": "0512c268", "metadata": {}, "outputs": [{"data": {"text/plain": ["1024"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["text=\"This is a tutorial on OPENAI embedding\"\n", "query_result=embeddings.embed_query(text)\n", "len(query_result)"]}, {"cell_type": "code", "execution_count": 15, "id": "ea88efe5", "metadata": {}, "outputs": [{"data": {"text/plain": ["[Document(metadata={'source': 'speech.txt'}, page_content='The world must be made safe for democracy. Its peace must be planted upon the tested foundations of political liberty. We have no selfish ends to serve. We desire no conquest, no dominion. We seek no indemnities for ourselves, no material compensation for the sacrifices we shall freely make. We are but one of the champions of the rights of mankind. We shall be satisfied when those rights have been made as secure as the faith and the freedom of nations can make them.\\n\\nJust because we fight without rancor and without selfish object, seeking nothing for ourselves but what we shall wish to share with all free peoples, we shall, I feel confident, conduct our operations as belligerents without passion and ourselves observe with proud punctilio the principles of right and of fair play we profess to be fighting for.\\n\\n…\\n\\nIt will be all the easier for us to conduct ourselves as belligerents in a high spirit of right and fairness because we act without animus, not in enmity toward a people or with the desire to bring any injury or disadvantage upon them, but only in armed opposition to an irresponsible government which has thrown aside all considerations of humanity and of right and is running amuck. We are, let me say again, the sincere friends of the German people, and shall desire nothing so much as the early reestablishment of intimate relations of mutual advantage between us—however hard it may be for them, for the time being, to believe that this is spoken from our hearts.\\n\\nWe have borne with their present government through all these bitter months because of that friendship—exercising a patience and forbearance which would otherwise have been impossible. We shall, happily, still have an opportunity to prove that friendship in our daily attitude and actions toward the millions of men and women of German birth and native sympathy who live among us and share our life, and we shall be proud to prove it toward all who are in fact loyal to their neighbors and to the government in the hour of test. They are, most of them, as true and loyal Americans as if they had never known any other fealty or allegiance. They will be prompt to stand with us in rebuking and restraining the few who may be of a different mind and purpose. If there should be disloyalty, it will be dealt with with a firm hand of stern repression; but, if it lifts its head at all, it will lift it only here and there and without countenance except from a lawless and malignant few.\\n\\nIt is a distressing and oppressive duty, gentlemen of the Congress, which I have performed in thus addressing you. There are, it may be, many months of fiery trial and sacrifice ahead of us. It is a fearful thing to lead this great peaceful people into war, into the most terrible and disastrous of all wars, civilization itself seeming to be in the balance. But the right is more precious than peace, and we shall fight for the things which we have always carried nearest our hearts—for democracy, for the right of those who submit to authority to have a voice in their own governments, for the rights and liberties of small nations, for a universal dominion of right by such a concert of free peoples as shall bring peace and safety to all nations and make the world itself at last free.\\n\\nTo such a task we can dedicate our lives and our fortunes, everything that we are and everything that we have, with the pride of those who know that the day has come when America is privileged to spend her blood and her might for the principles that gave her birth and happiness and the peace which she has treasured. God helping her, she can do no other.')]"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_community.document_loaders import TextLoader\n", "\n", "loader=TextLoader('speech.txt')\n", "docs=loader.load()\n", "docs"]}, {"cell_type": "code", "execution_count": 16, "id": "088922b5", "metadata": {}, "outputs": [{"data": {"text/plain": ["[Document(metadata={'source': 'speech.txt'}, page_content='The world must be made safe for democracy. Its peace must be planted upon the tested foundations of political liberty. We have no selfish ends to serve. We desire no conquest, no dominion. We seek no indemnities for ourselves, no material compensation for the sacrifices we shall freely make. We are but one of the champions of the rights of mankind. We shall be satisfied when those rights have been made as secure as the faith and the freedom of nations can make them.'),\n", " Document(metadata={'source': 'speech.txt'}, page_content='Just because we fight without rancor and without selfish object, seeking nothing for ourselves but what we shall wish to share with all free peoples, we shall, I feel confident, conduct our operations as belligerents without passion and ourselves observe with proud punctilio the principles of right and of fair play we profess to be fighting for.\\n\\n…'),\n", " Document(metadata={'source': 'speech.txt'}, page_content='It will be all the easier for us to conduct ourselves as belligerents in a high spirit of right and fairness because we act without animus, not in enmity toward a people or with the desire to bring any injury or disadvantage upon them, but only in armed opposition to an irresponsible government which has thrown aside all considerations of humanity and of right and is running amuck. We are, let me say again, the sincere friends of the German people, and shall desire nothing so much as the early'),\n", " Document(metadata={'source': 'speech.txt'}, page_content='and shall desire nothing so much as the early reestablishment of intimate relations of mutual advantage between us—however hard it may be for them, for the time being, to believe that this is spoken from our hearts.'),\n", " Document(metadata={'source': 'speech.txt'}, page_content='We have borne with their present government through all these bitter months because of that friendship—exercising a patience and forbearance which would otherwise have been impossible. We shall, happily, still have an opportunity to prove that friendship in our daily attitude and actions toward the millions of men and women of German birth and native sympathy who live among us and share our life, and we shall be proud to prove it toward all who are in fact loyal to their neighbors and to the'),\n", " Document(metadata={'source': 'speech.txt'}, page_content='are in fact loyal to their neighbors and to the government in the hour of test. They are, most of them, as true and loyal Americans as if they had never known any other fealty or allegiance. They will be prompt to stand with us in rebuking and restraining the few who may be of a different mind and purpose. If there should be disloyalty, it will be dealt with with a firm hand of stern repression; but, if it lifts its head at all, it will lift it only here and there and without countenance except'),\n", " Document(metadata={'source': 'speech.txt'}, page_content='here and there and without countenance except from a lawless and malignant few.'),\n", " Document(metadata={'source': 'speech.txt'}, page_content='It is a distressing and oppressive duty, gentlemen of the Congress, which I have performed in thus addressing you. There are, it may be, many months of fiery trial and sacrifice ahead of us. It is a fearful thing to lead this great peaceful people into war, into the most terrible and disastrous of all wars, civilization itself seeming to be in the balance. But the right is more precious than peace, and we shall fight for the things which we have always carried nearest our hearts—for democracy,'),\n", " Document(metadata={'source': 'speech.txt'}, page_content='always carried nearest our hearts—for democracy, for the right of those who submit to authority to have a voice in their own governments, for the rights and liberties of small nations, for a universal dominion of right by such a concert of free peoples as shall bring peace and safety to all nations and make the world itself at last free.'),\n", " Document(metadata={'source': 'speech.txt'}, page_content='To such a task we can dedicate our lives and our fortunes, everything that we are and everything that we have, with the pride of those who know that the day has come when America is privileged to spend her blood and her might for the principles that gave her birth and happiness and the peace which she has treasured. God helping her, she can do no other.')]"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_text_splitters import RecursiveCharacterTextSplitter\n", "\n", "text_splitter=RecursiveCharacterTextSplitter(chunk_size=500,chunk_overlap=50)\n", "final_documents=text_splitter.split_documents(docs)\n", "final_documents"]}, {"cell_type": "code", "execution_count": 21, "id": "3ead48c9", "metadata": {}, "outputs": [{"data": {"text/plain": ["'The world must be made safe for democracy. Its peace must be planted upon the tested foundations of political liberty. We have no selfish ends to serve. We desire no conquest, no dominion. We seek no indemnities for ourselves, no material compensation for the sacrifices we shall freely make. We are but one of the champions of the rights of mankind. We shall be satisfied when those rights have been made as secure as the faith and the freedom of nations can make them.'"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["final_documents[0].page_content"]}, {"cell_type": "code", "execution_count": 22, "id": "059634ff", "metadata": {}, "outputs": [{"data": {"text/plain": ["[0.029800165444612503,\n", " -0.0229507889598608,\n", " -0.006696968339383602,\n", " 0.07846301048994064,\n", " 0.014424929395318031,\n", " 0.011170579120516777,\n", " -0.02456451579928398,\n", " 0.02164187654852867,\n", " 0.034677207469940186,\n", " 0.03209524229168892,\n", " 0.019490240141749382,\n", " 0.13820677995681763,\n", " -0.03250763937830925,\n", " -0.018208222463726997,\n", " -0.005979756359010935,\n", " -0.020476406440138817,\n", " -0.034264810383319855,\n", " 0.01675586961209774,\n", " -0.05934930592775345,\n", " 0.001355979242362082,\n", " -0.024779679253697395,\n", " -0.0066880034282803535,\n", " -0.033009689301252365,\n", " -0.0018580277683213353,\n", " 0.032238684594631195,\n", " 0.029674652963876724,\n", " -0.008427242748439312,\n", " 0.011045066639780998,\n", " 0.04063006862998009,\n", " 0.07247428596019745,\n", " -0.0009968128288164735,\n", " 0.018522003665566444,\n", " 0.036165423691272736,\n", " -0.0029002265073359013,\n", " 0.08047120273113251,\n", " 0.011224370449781418,\n", " 0.008265869691967964,\n", " 0.018235119059681892,\n", " -0.018154432997107506,\n", " 0.06548146903514862,\n", " -0.008158287964761257,\n", " -0.024439003318548203,\n", " 0.04059420898556709,\n", " 0.0010791801614686847,\n", " 0.06268434226512909,\n", " 0.04299686849117279,\n", " -0.010794042609632015,\n", " -0.0010001747868955135,\n", " 0.017069648951292038,\n", " -0.06042512506246567,\n", " -0.008005880750715733,\n", " 0.002593170152977109,\n", " 0.03256143257021904,\n", " -0.00017285933427046984,\n", " -0.006495252717286348,\n", " -0.06332983076572418,\n", " 0.047443583607673645,\n", " -0.11131132394075394,\n", " -0.011385742574930191,\n", " -0.029979467391967773,\n", " -0.0023488698061555624,\n", " -0.00700626615434885,\n", " 0.04091695323586464,\n", " 0.06885236501693726,\n", " -0.04138313978910446,\n", " -0.035161323845386505,\n", " 0.0757376030087471,\n", " 0.01974126510322094,\n", " -0.006033547222614288,\n", " -0.027038898319005966,\n", " 0.01782272197306156,\n", " 0.0007822095067240298,\n", " -0.00038157927338033915,\n", " 0.05232062563300133,\n", " 0.03869359567761421,\n", " -0.0518902987241745,\n", " -0.010014074854552746,\n", " 0.009547886438667774,\n", " 0.04396510496735573,\n", " 0.03984113410115242,\n", " 0.043929245322942734,\n", " -0.049344196915626526,\n", " 0.050061408430337906,\n", " 0.02820436842739582,\n", " 0.035286836326122284,\n", " -0.0016058827750384808,\n", " -0.024313490837812424,\n", " -0.045650552958250046,\n", " 0.011735383421182632,\n", " -0.05422123894095421,\n", " 0.01398563664406538,\n", " 0.03690056502819061,\n", " 0.0012002097209915519,\n", " -0.008705162443220615,\n", " -0.032059382647275925,\n", " -0.0042562056332826614,\n", " -0.054364681243896484,\n", " -0.036165423691272736,\n", " -0.0009133248240686953,\n", " -0.05655217915773392,\n", " -0.044825758785009384,\n", " -0.0075083146803081036,\n", " -0.05475914850831032,\n", " 0.017804792150855064,\n", " -0.006961440201848745,\n", " 0.01144849881529808,\n", " -0.0001994746271520853,\n", " 0.072725310921669,\n", " 0.01688138023018837,\n", " 0.04005629941821098,\n", " 0.010856798849999905,\n", " 0.01404839288443327,\n", " 0.008283800445497036,\n", " 0.026572709903120995,\n", " -0.018450282514095306,\n", " -0.00662076473236084,\n", " 0.03865773603320122,\n", " -0.040522485971450806,\n", " -0.004697739612311125,\n", " 0.04877042770385742,\n", " -0.019956428557634354,\n", " 0.02217978611588478,\n", " 0.05540463700890541,\n", " -0.06512286514043808,\n", " -0.006580421235412359,\n", " 0.014290452003479004,\n", " -0.00526254391297698,\n", " 0.040952812880277634,\n", " -0.0027791971806436777,\n", " -0.012040198780596256,\n", " 0.01103610172867775,\n", " 0.035089604556560516,\n", " -0.024080397561192513,\n", " 0.018701307475566864,\n", " 0.07889333367347717,\n", " 0.05866795405745506,\n", " -0.030929774045944214,\n", " 0.028347810730338097,\n", " -0.018701307475566864,\n", " 0.05791487917304039,\n", " -0.06462081521749496,\n", " 0.05479500815272331,\n", " -0.047802191227674484,\n", " 0.018773028627038002,\n", " -0.007109365426003933,\n", " -0.003935701679438353,\n", " 0.04478989914059639,\n", " -0.04098867252469063,\n", " -0.024187980219721794,\n", " -0.019310936331748962,\n", " -0.034085508435964584,\n", " 0.030929774045944214,\n", " 0.00491738598793745,\n", " 0.009798911400139332,\n", " 0.001988022355362773,\n", " -0.0339958555996418,\n", " -0.03901633992791176,\n", " -0.005791488103568554,\n", " 0.008337590843439102,\n", " -0.009287897497415543,\n", " -0.01157401129603386,\n", " 0.010345784947276115,\n", " -0.01111678872257471,\n", " 0.017876513302326202,\n", " 0.01771513931453228,\n", " 0.041132114827632904,\n", " 0.018701307475566864,\n", " -0.007311081513762474,\n", " -0.016495879739522934,\n", " 0.04844767972826958,\n", " 0.003536752425134182,\n", " -0.0012293464969843626,\n", " 0.008041741326451302,\n", " -0.0009133248240686953,\n", " -0.014290452003479004,\n", " 0.008391382172703743,\n", " -0.06182368844747543,\n", " 0.019705403596162796,\n", " 0.010892659425735474,\n", " 0.0008046223665587604,\n", " 0.0006735070492140949,\n", " -0.017428254708647728,\n", " -0.07057367265224457,\n", " 0.03984113410115242,\n", " -0.02612445317208767,\n", " 0.09610642492771149,\n", " 0.05956446751952171,\n", " 0.04758702591061592,\n", " -0.004841181915253401,\n", " -0.004301031585782766,\n", " -0.004939798731356859,\n", " -0.002891261363402009,\n", " 0.04461059719324112,\n", " 0.0229687187820673,\n", " 0.023040439933538437,\n", " -0.06390359997749329,\n", " 0.028114715591073036,\n", " 0.03568130359053612,\n", " 0.017096543684601784,\n", " 0.05056345462799072,\n", " 0.05178271606564522,\n", " 0.004480334464460611,\n", " 0.060747869312763214,\n", " 0.03370897099375725,\n", " -0.05020485073328018,\n", " -0.034982021898031235,\n", " -0.0316290557384491,\n", " 0.012398804537951946,\n", " -0.0012562419287860394,\n", " 0.048519402742385864,\n", " 0.009772015735507011,\n", " -0.0007015231531113386,\n", " -0.036577820777893066,\n", " 0.024618307128548622,\n", " -0.020135730504989624,\n", " 0.00217741122469306,\n", " -0.0500972680747509,\n", " -0.039159782230854034,\n", " 0.006513183005154133,\n", " 0.02933397702872753,\n", " 0.01688138023018837,\n", " -0.020835012197494507,\n", " -0.03417515754699707,\n", " -0.0030234975274652243,\n", " -0.06827859580516815,\n", " -0.018396491184830666,\n", " 0.055906686931848526,\n", " 0.012219501659274101,\n", " 0.037581916898489,\n", " 0.06487184017896652,\n", " 0.02366800047457218,\n", " 0.031521473079919815,\n", " -0.005482190288603306,\n", " 0.05920586362481117,\n", " -0.04945177584886551,\n", " -0.02248460054397583,\n", " -0.0004849586694035679,\n", " -0.027343712747097015,\n", " -0.005208753049373627,\n", " -0.055799104273319244,\n", " 0.018351666629314423,\n", " -0.01766134984791279,\n", " 0.029065022245049477,\n", " -0.007181086577475071,\n", " 0.02897537127137184,\n", " 0.05052759498357773,\n", " -0.02635754644870758,\n", " -0.007638309150934219,\n", " -0.019597822800278664,\n", " -0.009673398919403553,\n", " 0.051854439079761505,\n", " -0.010498193092644215,\n", " -0.023470766842365265,\n", " 0.003236419754102826,\n", " 0.024259701371192932,\n", " 0.016962068155407906,\n", " 0.07147019356489182,\n", " -0.03833498805761337,\n", " -0.04981038346886635,\n", " -0.01746411621570587,\n", " 0.03715158998966217,\n", " -0.025066563859581947,\n", " 0.011690557934343815,\n", " 0.012784306891262531,\n", " -0.0057645924389362335,\n", " -0.013662891462445259,\n", " 0.005468742456287146,\n", " -0.0018994915299117565,\n", " 0.028491253033280373,\n", " -0.02676994353532791,\n", " -0.02594514936208725,\n", " -0.017751000821590424,\n", " -0.03559165447950363,\n", " -0.031826287508010864,\n", " 0.0062980190850794315,\n", " 0.012622933834791183,\n", " 0.04102453589439392,\n", " 0.0229507889598608,\n", " -0.00930582731962204,\n", " 0.006087338086217642,\n", " 0.024349352344870567,\n", " -0.03851429373025894,\n", " -1.0129570910066832e-05,\n", " -0.06881650537252426,\n", " -0.025030704215168953,\n", " 0.012183641083538532,\n", " -0.006849376019090414,\n", " -0.018934400752186775,\n", " -0.042494818568229675,\n", " -0.005150479730218649,\n", " -0.061214055866003036,\n", " 0.03948253020644188,\n", " -0.007970020174980164,\n", " 0.01712344028055668,\n", " 0.02832987904548645,\n", " 0.0307146105915308,\n", " 0.03482064977288246,\n", " 0.01653173938393593,\n", " -0.07365768402814865,\n", " 0.0459374375641346,\n", " 0.04611673951148987,\n", " 0.012506387196481228,\n", " 0.010067865252494812,\n", " 0.040952812880277634,\n", " 0.04059420898556709,\n", " 0.023004580289125443,\n", " -0.023829374462366104,\n", " 0.011340917088091373,\n", " 0.05730525031685829,\n", " -0.006750759202986956,\n", " -0.0015980383614078164,\n", " -0.03535855934023857,\n", " 0.057161808013916016,\n", " 0.037832941859960556,\n", " -0.047802191227674484,\n", " 0.02999739721417427,\n", " 0.046009160578250885,\n", " -0.0067955851554870605,\n", " 0.05414951592683792,\n", " 0.013250494375824928,\n", " 0.02540723979473114,\n", " -0.0017033788608387113,\n", " -0.01404839288443327,\n", " -0.004702222067862749,\n", " -0.008718609809875488,\n", " -0.06343741714954376,\n", " 0.0016507086111232638,\n", " -0.017437219619750977,\n", " -0.021552225574851036,\n", " -0.012327083386480808,\n", " 0.0013290838105604053,\n", " -0.01270361989736557,\n", " 0.014155974611639977,\n", " -0.028598833829164505,\n", " 0.059062421321868896,\n", " -0.033906202763319016,\n", " 0.025425171479582787,\n", " -0.033852413296699524,\n", " 0.019400589168071747,\n", " -0.02135499194264412,\n", " 0.046547066420316696,\n", " -0.0637960210442543,\n", " -0.031575266271829605,\n", " -0.008781366050243378,\n", " 0.02122947946190834,\n", " 0.03919564560055733,\n", " 0.025299658998847008,\n", " -0.01100920606404543,\n", " -0.00014078090316616,\n", " 0.02051226794719696,\n", " 0.0009435822139494121,\n", " -0.021516364067792892,\n", " 0.09165970981121063,\n", " 0.044395431876182556,\n", " 0.038442570716142654,\n", " 0.04765874892473221,\n", " 0.006100785918533802,\n", " -0.00876343622803688,\n", " -0.03794052079319954,\n", " -0.02641133777797222,\n", " 0.013698752038180828,\n", " 0.03722330927848816,\n", " -0.00980787631124258,\n", " 0.06078372895717621,\n", " -0.029065022245049477,\n", " -0.039159782230854034,\n", " -0.00572424940764904,\n", " -0.03959010913968086,\n", " -0.014415964484214783,\n", " 0.05414951592683792,\n", " 0.003937942907214165,\n", " -0.001201330334879458,\n", " 0.011565045453608036,\n", " -0.016720008105039597,\n", " -0.04077351093292236,\n", " -0.02445693500339985,\n", " 0.035448212176561356,\n", " 0.014944908209145069,\n", " -0.019633682444691658,\n", " -0.007181086577475071,\n", " -0.022394949570298195,\n", " 0.026214104145765305,\n", " 0.005060828290879726,\n", " 0.019346797838807106,\n", " 0.0007211344200186431,\n", " -0.01706068404018879,\n", " 0.0015980383614078164,\n", " 0.03786880150437355,\n", " -0.032184895128011703,\n", " 0.003574854228645563,\n", " -0.05479500815272331,\n", " 0.0339958555996418,\n", " -0.0334041565656662,\n", " -0.03526890650391579,\n", " -0.05795074254274368,\n", " 0.06286364793777466,\n", " 0.008933774195611477,\n", " -0.009207210503518581,\n", " -0.00808656681329012,\n", " -0.027469225227832794,\n", " -0.016971033066511154,\n", " -0.01533040963113308,\n", " -0.011439533904194832,\n", " -0.01058784406632185,\n", " 0.05414951592683792,\n", " -0.05171099677681923,\n", " 0.005137031897902489,\n", " 0.00894722156226635,\n", " -0.010578879155218601,\n", " -0.0017078614328056574,\n", " -0.044108547270298004,\n", " 0.03833498805761337,\n", " 0.04920075461268425,\n", " 0.036595750600099564,\n", " -0.03035600483417511,\n", " 0.04299686849117279,\n", " 0.01765238307416439,\n", " -0.03487443923950195,\n", " 0.030624957755208015,\n", " -0.06684417277574539,\n", " -0.031091146171092987,\n", " -0.052930258214473724,\n", " 0.06713105738162994,\n", " -0.008243457414209843,\n", " -0.009005495347082615,\n", " -0.019902637228369713,\n", " 0.04220793396234512,\n", " -0.035107534378767014,\n", " -0.034318599849939346,\n", " -0.06060442700982094,\n", " -0.011941581964492798,\n", " 0.006956957746297121,\n", " -0.004126211162656546,\n", " -0.019579891115427017,\n", " 0.05153169110417366,\n", " -0.048160795122385025,\n", " -0.03930322453379631,\n", " -0.039446666836738586,\n", " -0.011161614209413528,\n", " 0.05468742549419403,\n", " -0.001575625385157764,\n", " -0.04669051244854927,\n", " -0.03469513729214668,\n", " -0.06752552092075348,\n", " -0.07552243769168854,\n", " 0.016979997977614403,\n", " 0.00868274923413992,\n", " -0.005917000118643045,\n", " 0.024295561015605927,\n", " 2.339344246138353e-05,\n", " 0.021516364067792892,\n", " -0.006746276747435331,\n", " -0.005374608561396599,\n", " -0.028401600196957588,\n", " -0.03564544394612312,\n", " -0.07767407596111298,\n", " -0.021928761154413223,\n", " -0.06035340204834938,\n", " 0.002709717024117708,\n", " 0.027092689648270607,\n", " 0.08441586792469025,\n", " 0.04005629941821098,\n", " 0.05655217915773392,\n", " -0.02504863403737545,\n", " -0.03865773603320122,\n", " 0.026536850258708,\n", " 0.03564544394612312,\n", " 0.025729985907673836,\n", " 0.0024990360252559185,\n", " -0.041956909000873566,\n", " -0.05027657002210617,\n", " 0.023721791803836823,\n", " 0.010874729603528976,\n", " 0.021749459207057953,\n", " -0.019400589168071747,\n", " 0.01843235269188881,\n", " -0.003299175761640072,\n", " -0.07075297832489014,\n", " 0.020440546795725822,\n", " -0.03804810345172882,\n", " -0.02779197134077549,\n", " -0.024187980219721794,\n", " 0.00805070623755455,\n", " -0.04894972965121269,\n", " -0.0322207547724247,\n", " -0.020010218024253845,\n", " -0.0038191545754671097,\n", " 0.026572709903120995,\n", " -0.048878006637096405,\n", " 0.025801707059144974,\n", " 0.0028060926124453545,\n", " -0.02971051260828972,\n", " -0.04733600094914436,\n", " 0.031109075993299484,\n", " -0.011851930990815163,\n", " -0.011762279085814953,\n", " -0.023721791803836823,\n", " -0.0070914351381361485,\n", " 0.02571205608546734,\n", " 0.005894587375223637,\n", " 0.008037258870899677,\n", " 0.034264810383319855,\n", " 0.06343741714954376,\n", " 0.01189675647765398,\n", " 0.017697209492325783,\n", " -0.011322986334562302,\n", " 0.026644431054592133,\n", " -0.08104497194290161,\n", " -0.007790716830641031,\n", " 0.03379862383008003,\n", " 0.036595750600099564,\n", " -0.0009671157458797097,\n", " 0.017571697011590004,\n", " -0.019364727661013603,\n", " 0.002602135296911001,\n", " 0.018522003665566444,\n", " -0.012927749194204807,\n", " 0.0256403349339962,\n", " 0.025443101301789284,\n", " -0.014801465906202793,\n", " 0.04457473382353783,\n", " 0.020422615110874176,\n", " -0.04443129152059555,\n", " 0.03240006044507027,\n", " 0.043678220361471176,\n", " 0.04938005656003952,\n", " -0.005683906376361847,\n", " -0.0036174387205392122,\n", " -0.11288919299840927,\n", " 0.02004607953131199,\n", " 0.07043023407459259,\n", " 0.048160795122385025,\n", " 0.021606015041470528,\n", " -0.058237627148628235,\n", " 0.011071962304413319,\n", " -0.007512797135859728,\n", " -0.03482064977288246,\n", " 0.012174676172435284,\n", " 0.016558635979890823,\n", " -0.024349352344870567,\n", " 0.0542570985853672,\n", " -0.02373972162604332,\n", " 0.03976941481232643,\n", " 0.01585935428738594,\n", " 0.018333734944462776,\n", " 0.007432111073285341,\n", " 0.016612425446510315,\n", " -0.051495831459760666,\n", " 0.004439991433173418,\n", " 0.020261242985725403,\n", " 0.04045076668262482,\n", " -0.026877524331212044,\n", " -0.07064539939165115,\n", " 0.03076840192079544,\n", " 0.03675712272524834,\n", " -0.037832941859960556,\n", " 0.04873456433415413,\n", " -0.0010035366285592318,\n", " -0.02171359769999981,\n", " -0.028311949223279953,\n", " 0.014084253460168839,\n", " -0.004540849477052689,\n", " 0.038442570716142654,\n", " -0.02881399728357792,\n", " 0.010085796006023884,\n", " -0.03851429373025894,\n", " -0.01889853924512863,\n", " 0.024349352344870567,\n", " -0.004782908596098423,\n", " -0.03905219957232475,\n", " -0.027397504076361656,\n", " -0.02266390435397625,\n", " 0.014191835187375546,\n", " 0.014362173154950142,\n", " -0.015697980299592018,\n", " -0.023219743743538857,\n", " 0.030642889440059662,\n", " -0.02689545601606369,\n", " -0.028437461704015732,\n", " -0.028706416487693787,\n", " 0.021498434245586395,\n", " -0.0057287318632006645,\n", " -0.02332732453942299,\n", " 0.033673111349344254,\n", " -0.038729455322027206,\n", " 0.01407528854906559,\n", " -0.034085508435964584,\n", " -0.016899311915040016,\n", " 0.022215645760297775,\n", " -0.03374483063817024,\n", " -0.001321239280514419,\n", " 0.035519931465387344,\n", " 0.013026366010308266,\n", " -0.005190822761505842,\n", " 0.021372921764850616,\n", " -0.007387285120785236,\n", " -0.005419434048235416,\n", " -0.006575938779860735,\n", " 0.027469225227832794,\n", " -0.010901624336838722,\n", " 0.0013795127160847187,\n", " 0.02343490719795227,\n", " 0.04009215906262398,\n", " -0.00278816232457757,\n", " -0.018468212336301804,\n", " -0.004238275345414877,\n", " -0.015366270206868649,\n", " -0.043929245322942734,\n", " 0.024546585977077484,\n", " 0.05407779663801193,\n", " -0.027236131951212883,\n", " -0.012228467501699924,\n", " -0.04733600094914436,\n", " -0.0193826574832201,\n", " 0.024474864825606346,\n", " 0.009234106168150902,\n", " 0.04012801870703697,\n", " 0.03240006044507027,\n", " -0.00233990466222167,\n", " -0.004608088172972202,\n", " 0.019633682444691658,\n", " -0.07530727237462997,\n", " 0.014577336609363556,\n", " -0.0014590785140171647,\n", " 0.032113172113895416,\n", " 0.05927758291363716,\n", " -0.018504073843359947,\n", " -0.013680821284651756,\n", " -0.024403143674135208,\n", " 0.015850387513637543,\n", " -0.0038146721199154854,\n", " -0.04331961274147034,\n", " 0.02261011302471161,\n", " 0.016298646107316017,\n", " -0.028060926124453545,\n", " 0.004841181915253401,\n", " -0.015993831679224968,\n", " -0.03191594034433365,\n", " 0.010417506098747253,\n", " -0.024116259068250656,\n", " 0.04407268762588501,\n", " 0.024707958102226257,\n", " 0.022735625505447388,\n", " 0.001956644468009472,\n", " 0.005795970559120178,\n", " 0.06411876529455185,\n", " 0.029244326055049896,\n", " 0.02320181205868721,\n", " 0.03514339402318001,\n", " -0.010381645523011684,\n", " -0.001614847918972373,\n", " -0.018504073843359947,\n", " -0.0049801417626440525,\n", " 0.015312478877604008,\n", " -0.024958983063697815,\n", " -0.007656239438802004,\n", " -0.007060057017952204,\n", " -0.033314503729343414,\n", " 0.02402660623192787,\n", " 0.015733841806650162,\n", " -0.01746411621570587,\n", " -0.005226683337241411,\n", " 0.000941340927965939,\n", " 0.0029966020956635475,\n", " -0.002063105581328273,\n", " -0.03473099693655968,\n", " 0.02320181205868721,\n", " 0.01752687245607376,\n", " 0.014828361570835114,\n", " -0.006907649338245392,\n", " 0.06939027458429337,\n", " -0.019310936331748962,\n", " 0.009763049893081188,\n", " -0.001726912334561348,\n", " -0.0029495349153876305,\n", " -0.04633190482854843,\n", " -0.027003036811947823,\n", " 0.013501518405973911,\n", " -0.021677736192941666,\n", " -0.0005225002532824874,\n", " 0.06967715919017792,\n", " -0.013913915492594242,\n", " 0.028491253033280373,\n", " -0.02063778042793274,\n", " 0.08742816001176834,\n", " -0.009041355922818184,\n", " 0.03704400733113289,\n", " 0.014093218371272087,\n", " 0.029549140483140945,\n", " 0.031826287508010864,\n", " 0.010892659425735474,\n", " -0.003043669043108821,\n", " -0.020117800682783127,\n", " 0.006436978932470083,\n", " 0.0071272957138717175,\n", " -0.021928761154413223,\n", " 0.05038415268063545,\n", " -0.02391902543604374,\n", " -0.051854439079761505,\n", " 0.03370897099375725,\n", " -0.05612185224890709,\n", " 0.01362703088670969,\n", " -0.010005109012126923,\n", " -0.005271509289741516,\n", " 0.02422383986413479,\n", " 0.032884176820516586,\n", " 0.026465129107236862,\n", " -0.03284831717610359,\n", " -0.05145997181534767,\n", " -0.013331180438399315,\n", " -0.009108594618737698,\n", " 0.050312433391809464,\n", " 0.013376006856560707,\n", " 0.005648045800626278,\n", " 0.03406757861375809,\n", " -0.010659565217792988,\n", " 0.0163972619920969,\n", " 0.026572709903120995,\n", " -0.041777607053518295,\n", " 0.0012349496828392148,\n", " 0.02320181205868721,\n", " -0.010713356547057629,\n", " -0.013519449159502983,\n", " -0.0038594978395849466,\n", " -0.024062467738986015,\n", " -0.04378580302000046,\n", " -0.04245895892381668,\n", " 0.014227695763111115,\n", " -0.005683906376361847,\n", " -0.00010681137791834772,\n", " 0.038442570716142654,\n", " -0.033009689301252365,\n", " -0.004939798731356859,\n", " 0.007033161818981171,\n", " -0.02350662834942341,\n", " -0.009951318614184856,\n", " -0.015895213931798935,\n", " -0.009216176345944405,\n", " -0.027469225227832794,\n", " -0.0068673063069581985,\n", " 0.019759194925427437,\n", " -0.00255730957724154,\n", " 0.016953101381659508,\n", " 0.0140125323086977,\n", " -0.02987188659608364,\n", " 0.015070419758558273,\n", " 0.02361420914530754,\n", " 0.03482064977288246,\n", " -0.008956186473369598,\n", " -0.03797638416290283,\n", " 0.031055286526679993,\n", " -0.028903650119900703,\n", " 0.0002818419598042965,\n", " -0.02463623695075512,\n", " 0.00415758928284049,\n", " 0.0238652341067791,\n", " 0.034623418003320694,\n", " 0.016200030222535133,\n", " -0.01884474977850914,\n", " 0.06956958025693893,\n", " -0.025783777236938477,\n", " 0.012802236713469028,\n", " -0.025299658998847008,\n", " -0.024887261912226677,\n", " -0.007808647118508816,\n", " -0.0018132019322365522,\n", " -0.006822480354458094,\n", " 0.0015128693776205182,\n", " 0.027756109833717346,\n", " 0.04945177584886551,\n", " 0.03209524229168892,\n", " 0.012156745418906212,\n", " -0.0018714754842221737,\n", " -0.012246397323906422,\n", " -0.03196972981095314,\n", " -0.022574251517653465,\n", " -0.012040198780596256,\n", " -5.617228089249693e-05,\n", " -0.0005883380654267967,\n", " -0.021982552483677864,\n", " -0.014424929395318031,\n", " 0.02015366218984127,\n", " -0.01628968119621277,\n", " 0.002373524010181427,\n", " -0.007015231531113386,\n", " -0.021086037158966064,\n", " -0.030553236603736877,\n", " 0.0016305369790643454,\n", " 0.02868848666548729,\n", " 0.04945177584886551,\n", " -0.04859112203121185,\n", " 0.0012730516027659178,\n", " 0.005867691710591316,\n", " 0.039088062942028046,\n", " 0.018333734944462776,\n", " -0.005648045800626278,\n", " 0.0031848701182752848,\n", " -0.011556080542504787,\n", " -0.05866795405745506,\n", " -0.019418518990278244,\n", " -0.02469002828001976,\n", " 0.007781751919537783,\n", " 0.0023645588662475348,\n", " 0.04063006862998009,\n", " -0.006078372709453106,\n", " -0.007158673834055662,\n", " -0.022771485149860382,\n", " 0.0073783197440207005,\n", " 0.003958114422857761,\n", " 0.020494336262345314,\n", " 0.03765363618731499,\n", " -0.01633450575172901,\n", " 0.013295319862663746,\n", " 0.0153573052957654,\n", " -0.0005894587375223637,\n", " 0.013223598711192608,\n", " 0.06630626320838928,\n", " -0.004581192508339882,\n", " -0.041419003158807755,\n", " -0.003976044710725546,\n", " -0.051316529512405396,\n", " -0.009323758073151112,\n", " -0.027720250189304352,\n", " 0.017688244581222534,\n", " -0.0010248288745060563,\n", " 0.013071191497147083,\n", " 0.05339644476771355,\n", " 0.002255856292322278,\n", " 0.028007134795188904,\n", " -0.020494336262345314,\n", " 0.0031378031708300114,\n", " 0.04669051244854927,\n", " -0.02348869852721691,\n", " 0.05171099677681923,\n", " -0.03797638416290283,\n", " 0.03263315185904503,\n", " 0.01055198349058628,\n", " -0.0023062853142619133,\n", " 0.011457463726401329,\n", " 0.04884214699268341,\n", " -0.02002814970910549,\n", " -0.025120355188846588,\n", " -0.054543983191251755,\n", " 0.012658794410526752,\n", " 0.02074536122381687,\n", " 0.027056828141212463,\n", " 0.015222827903926373,\n", " -0.05669562146067619,\n", " -0.016917241737246513,\n", " -0.020960524678230286,\n", " 0.027415433898568153,\n", " -0.016657251864671707,\n", " -0.014675953425467014,\n", " -0.02110396698117256,\n", " -0.006020099390298128,\n", " 0.019006121903657913,\n", " -0.038012243807315826,\n", " -0.029369836673140526,\n", " -0.007934159599244595,\n", " 0.037581916898489,\n", " 0.0014254591660574079,\n", " 0.046798091381788254,\n", " 0.034802719950675964,\n", " 0.015070419758558273,\n", " 0.013895985670387745,\n", " -0.0033596905414015055,\n", " 0.027056828141212463,\n", " -0.016370367258787155,\n", " -0.019310936331748962,\n", " -0.03607577085494995,\n", " 0.02766645886003971,\n", " -0.0006572576821781695,\n", " -0.020781222730875015,\n", " -0.03232833743095398,\n", " -0.00533874798566103,\n", " -0.006575938779860735,\n", " -0.004249481949955225,\n", " -0.05884725600481033,\n", " 0.02397281676530838,\n", " 0.06953372061252594,\n", " -0.025317588821053505,\n", " -0.0011441775131970644,\n", " 0.05956446751952171,\n", " -0.020906733348965645,\n", " -0.021910831332206726,\n", " -0.004756012931466103,\n", " 0.006419048644602299,\n", " -0.015115246176719666,\n", " 0.01615520380437374,\n", " 0.029782233759760857,\n", " 0.023273533210158348,\n", " 0.007557623088359833,\n", " 0.04253068193793297,\n", " -0.027953343465924263,\n", " 0.01574280671775341,\n", " 0.0333862267434597,\n", " -0.02176738902926445,\n", " -0.005612185224890709,\n", " -0.04453887417912483,\n", " 0.03765363618731499,\n", " -0.00021306243434082717,\n", " -0.030804261565208435,\n", " 0.006549043580889702,\n", " 0.008059671148657799,\n", " -0.02861676551401615,\n", " -0.005858726799488068,\n", " 0.040881093591451645,\n", " 0.052643369883298874,\n", " -0.007140743546187878,\n", " -0.028042994439601898,\n", " 0.05336058512330055,\n", " -0.022466670721769333,\n", " -0.021498434245586395,\n", " -0.015832457691431046,\n", " 0.002602135296911001,\n", " -0.007943124510347843,\n", " 0.01615520380437374,\n", " 0.012389839626848698,\n", " -0.03394206613302231,\n", " -0.005813900846987963,\n", " -0.0030907359905540943,\n", " -0.0024564515333622694,\n", " -0.02002814970910549,\n", " 0.03422895073890686,\n", " -0.012631898745894432,\n", " 0.04066592827439308,\n", " 0.001357099856249988,\n", " -0.004067937843501568,\n", " -0.04335547238588333,\n", " 0.061214055866003036,\n", " -0.03627300262451172,\n", " 0.026447197422385216,\n", " -0.005056345835328102,\n", " -0.02076329104602337,\n", " 0.008628958836197853,\n", " -0.03238212689757347,\n", " -0.030571168288588524,\n", " 0.030338073149323463,\n", " -0.033368296921253204,\n", " 0.005908035207539797,\n", " -0.02361420914530754,\n", " -0.01324152946472168,\n", " -0.005092206411063671,\n", " -0.018145468086004257,\n", " 0.020440546795725822,\n", " -0.014245626516640186,\n", " 0.05110136419534683,\n", " 0.04862698167562485,\n", " 0.015151106752455235,\n", " 0.013761508278548717,\n", " 0.014774570241570473,\n", " 0.015877284109592438,\n", " 0.006212850101292133,\n", " -0.012049163691699505,\n", " -0.015446956269443035,\n", " -0.002067588036879897,\n", " -0.01920335553586483,\n", " 0.02630375511944294,\n", " 0.004146382678300142,\n", " 0.006280088797211647,\n", " 0.05511775240302086,\n", " -0.024654166772961617,\n", " 0.017222056165337563,\n", " -0.01270361989736557,\n", " -0.0417417474091053,\n", " -0.004146382678300142,\n", " 0.007557623088359833,\n", " -0.03482064977288246,\n", " 0.015007664449512959,\n", " 0.031754568219184875,\n", " 0.016065552830696106,\n", " -0.017168264836072922,\n", " 0.013286354951560497,\n", " -0.02635754644870758,\n", " 0.027361642569303513,\n", " -0.0196695439517498,\n", " 0.02971051260828972,\n", " 0.01398563664406538,\n", " -0.005235648714005947,\n", " -0.004756012931466103,\n", " 0.027218200266361237,\n", " -0.01717723160982132,\n", " -0.026034800335764885,\n", " -0.009431339800357819,\n", " -0.00847206823527813,\n", " -0.028258157894015312,\n", " 0.030858052894473076,\n", " 0.003868462983518839,\n", " 0.00016697595128789544,\n", " 0.017804792150855064,\n", " -0.01056094840168953,\n", " -0.014335277490317822,\n", " -0.01861165463924408,\n", " 0.017921337857842445,\n", " -0.009987179189920425,\n", " 0.001656311796978116,\n", " -0.018396491184830666,\n", " 0.029674652963876724,\n", " -0.010426471941173077,\n", " -0.009314793162047863,\n", " ...]"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["embeddings.embed_query(final_documents[0].page_content)"]}, {"cell_type": "code", "execution_count": null, "id": "ffb7505f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.0"}}, "nbformat": 4, "nbformat_minor": 5}