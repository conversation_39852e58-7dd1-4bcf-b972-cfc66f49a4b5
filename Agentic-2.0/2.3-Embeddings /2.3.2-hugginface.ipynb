{"cells": [{"cell_type": "code", "execution_count": 9, "id": "495c8c69", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["import os\n", "from dotenv import load_dotenv\n", "load_dotenv()  #load all the environment variables\n", "\n", "#https://python.langchain.com/docs/integrations/text_embedding/"]}, {"cell_type": "code", "execution_count": 2, "id": "a778c28c", "metadata": {}, "outputs": [], "source": ["os.environ['HF_TOKEN']=os.getenv(\"HF_TOKEN\")"]}, {"cell_type": "markdown", "id": "78d0c67c", "metadata": {}, "source": ["#### Sentence Transformers on Hugging Face\n", "Hugging Face sentence-transformers is a Python framework for state-of-the-art sentence, text and image embeddings. One of the embedding models is used in the HuggingFaceEmbeddings class. We have also added an alias for SentenceTransformerEmbeddings for users who are more familiar with directly using that package."]}, {"cell_type": "code", "execution_count": 3, "id": "443bcc49", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["e:\\agenticbatch2\\venv\\Lib\\site-packages\\tqdm\\auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n", "e:\\agenticbatch2\\venv\\Lib\\site-packages\\huggingface_hub\\file_download.py:143: UserWarning: `huggingface_hub` cache-system uses symlinks by default to efficiently store duplicated files but your machine does not support them in C:\\Users\\<USER>\\.cache\\huggingface\\hub\\models--sentence-transformers--all-MiniLM-L6-v2. Caching files will still work but in a degraded version that might require more space on your disk. This warning can be disabled by setting the `HF_HUB_DISABLE_SYMLINKS_WARNING` environment variable. For more details, see https://huggingface.co/docs/huggingface_hub/how-to-cache#limitations.\n", "To support symlinks on Windows, you either need to activate Developer Mode or to run Python as an administrator. In order to activate developer mode, see this article: https://docs.microsoft.com/en-us/windows/apps/get-started/enable-your-device-for-development\n", "  warnings.warn(message)\n", "Xet Storage is enabled for this repo, but the 'hf_xet' package is not installed. Falling back to regular HTTP download. For better performance, install the package with: `pip install huggingface_hub[hf_xet]` or `pip install hf_xet`\n"]}], "source": ["from langchain_huggingface import HuggingFaceEmbeddings\n", "embeddings=HuggingFaceEmbeddings(model_name=\"all-MiniLM-L6-v2\")"]}, {"cell_type": "code", "execution_count": 5, "id": "42870391", "metadata": {}, "outputs": [{"data": {"text/plain": ["HuggingFaceEmbeddings(model_name='all-MiniLM-L6-v2', cache_folder=None, model_kwargs={}, encode_kwargs={}, query_encode_kwargs={}, multi_process=False, show_progress=False)"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["embeddings"]}, {"cell_type": "code", "execution_count": 6, "id": "54e4131f", "metadata": {}, "outputs": [{"data": {"text/plain": ["[-0.04311221092939377,\n", " 0.13562120497226715,\n", " 0.022339949384331703,\n", " 0.007216726895421743,\n", " 0.03421058505773544,\n", " 0.02403411455452442,\n", " -0.024848800152540207,\n", " 0.04566732048988342,\n", " 0.01885058917105198,\n", " 0.04899345710873604,\n", " -0.004306062590330839,\n", " 0.05968935415148735,\n", " 0.0029523137491196394,\n", " -0.059990864247083664,\n", " -0.11980380117893219,\n", " -0.005690607707947493,\n", " -0.02096841111779213,\n", " 0.009721233509480953,\n", " 0.040234461426734924,\n", " 0.050469983369112015,\n", " -0.0021607803646475077,\n", " 0.0988808125257492,\n", " 0.021964730694890022,\n", " -0.058519911020994186,\n", " 0.02956194058060646,\n", " 0.0041176509112119675,\n", " -0.09333009272813797,\n", " -0.043055254966020584,\n", " 0.06968395411968231,\n", " -0.04684080928564072,\n", " 0.043953247368335724,\n", " 0.010073350742459297,\n", " 0.09620823711156845,\n", " 0.027930215001106262,\n", " 0.07333724945783615,\n", " -0.012976894155144691,\n", " 0.0761367678642273,\n", " -0.011923190206289291,\n", " 0.011215261183679104,\n", " -0.008163194172084332,\n", " -0.010897370055317879,\n", " -0.07058067619800568,\n", " -0.027595993131399155,\n", " -0.006153104826807976,\n", " 0.046432070434093475,\n", " 0.05815570428967476,\n", " -0.03704500570893288,\n", " -0.017532598227262497,\n", " -0.045897096395492554,\n", " 0.052651356905698776,\n", " -0.1262217015028,\n", " -0.06410441547632217,\n", " -0.0004425861407071352,\n", " -0.003430652664974332,\n", " 0.04251398146152496,\n", " 0.04050419479608536,\n", " 0.012634390965104103,\n", " 0.017847348004579544,\n", " -0.08135197311639786,\n", " -0.04575568810105324,\n", " 0.011493401601910591,\n", " 0.02473783865571022,\n", " -0.11365985870361328,\n", " 0.02093517780303955,\n", " 0.08893711119890213,\n", " 0.07152356952428818,\n", " 0.016180450096726418,\n", " 0.007937238551676273,\n", " -0.004844018258154392,\n", " -0.07771333307027817,\n", " -0.02475927211344242,\n", " -0.006974433083087206,\n", " -0.009442328475415707,\n", " 0.025737030431628227,\n", " -0.028031999245285988,\n", " -0.007582941558212042,\n", " 0.0012087420327588916,\n", " 0.021890752017498016,\n", " -0.014229576103389263,\n", " -0.040772128850221634,\n", " -0.03103705681860447,\n", " -0.011769533157348633,\n", " -0.01741417683660984,\n", " -0.005468812305480242,\n", " -0.021282631903886795,\n", " 0.003637464717030525,\n", " -0.028120914474129677,\n", " -0.07080923020839691,\n", " -0.019715867936611176,\n", " 0.014832265675067902,\n", " 0.026327241212129593,\n", " -0.10834400355815887,\n", " 0.059244852513074875,\n", " 0.006977130193263292,\n", " -0.03420635685324669,\n", " -0.08328741043806076,\n", " 0.01941869407892227,\n", " 0.031528402119874954,\n", " 0.09671326726675034,\n", " 0.08736552298069,\n", " 0.000458756519947201,\n", " 0.05562618747353554,\n", " 0.028303414583206177,\n", " -0.007492845878005028,\n", " -0.0823916420340538,\n", " -0.05510355532169342,\n", " -0.0359821654856205,\n", " -0.0661410465836525,\n", " 0.023383520543575287,\n", " -0.010342122986912727,\n", " -0.02513558603823185,\n", " -0.04364794120192528,\n", " -0.09303712099790573,\n", " -0.08427291363477707,\n", " -0.005717206746339798,\n", " -0.047260090708732605,\n", " -0.05406207591295242,\n", " -0.0063044182024896145,\n", " 0.06324262917041779,\n", " 0.006845077034085989,\n", " 0.00730482442304492,\n", " -0.03741742670536041,\n", " -0.06619559973478317,\n", " -0.03141273930668831,\n", " -0.04530562087893486,\n", " -0.13435550034046173,\n", " 0.06425634026527405,\n", " -3.7944575608879424e-33,\n", " 0.00920464564114809,\n", " -0.0026120871771126986,\n", " -0.05146428942680359,\n", " 0.02832680381834507,\n", " 0.015869436785578728,\n", " -0.01839016191661358,\n", " 0.009616601280868053,\n", " -0.0590033233165741,\n", " -0.07848985493183136,\n", " -0.058791980147361755,\n", " -0.05810655280947685,\n", " -0.0037340777926146984,\n", " 0.01943146623671055,\n", " 0.03566226363182068,\n", " -0.012873196043074131,\n", " 0.01755511946976185,\n", " -0.05345636233687401,\n", " 0.10923223942518234,\n", " 0.05307411774992943,\n", " 0.018666168674826622,\n", " 0.01107385940849781,\n", " 0.018629441037774086,\n", " 0.01849411614239216,\n", " -0.03328318893909454,\n", " 0.08108535408973694,\n", " 0.019819989800453186,\n", " 0.0036039194092154503,\n", " -0.052251506596803665,\n", " -0.016028713434934616,\n", " 0.03214477747678757,\n", " 0.07309543341398239,\n", " -0.010538766160607338,\n", " -0.014300359413027763,\n", " -0.07095123082399368,\n", " 0.015454085543751717,\n", " 0.0458153635263443,\n", " 0.013757502660155296,\n", " -0.057602595537900925,\n", " 0.02660333923995495,\n", " -0.010926507413387299,\n", " 0.037021007388830185,\n", " -0.03239741548895836,\n", " 0.10511928051710129,\n", " 0.013426820747554302,\n", " 0.03451133146882057,\n", " 0.08210961520671844,\n", " 0.013097463175654411,\n", " 0.028171401470899582,\n", " 0.17125359177589417,\n", " 0.024312695488333702,\n", " -0.004523233510553837,\n", " -0.029154513031244278,\n", " -0.0767710730433464,\n", " -0.04611408710479736,\n", " 0.04300982505083084,\n", " 0.016595032066106796,\n", " -0.030377445742487907,\n", " 0.06007709726691246,\n", " 0.008674864657223225,\n", " 0.04707978293299675,\n", " -0.006106669083237648,\n", " 0.04548472911119461,\n", " -0.013986147940158844,\n", " -0.018740149214863777,\n", " -0.05967798829078674,\n", " -0.05526155233383179,\n", " -0.03951441869139671,\n", " -0.026357758790254593,\n", " 0.06813979893922806,\n", " -0.050848349928855896,\n", " -0.11309295147657394,\n", " 0.03381052613258362,\n", " 0.03922300413250923,\n", " 0.0016803489997982979,\n", " -0.03943445906043053,\n", " 0.047680485993623734,\n", " 0.026041703298687935,\n", " -0.011414709500968456,\n", " -0.051370564848184586,\n", " 0.013942476361989975,\n", " -0.14343895018100739,\n", " -0.026646504178643227,\n", " 0.04094989597797394,\n", " 0.05247935280203819,\n", " 0.014763260260224342,\n", " 0.043576307594776154,\n", " 0.027194926515221596,\n", " -0.004931184928864241,\n", " 0.019762316718697548,\n", " 0.053597137331962585,\n", " -0.03546648845076561,\n", " -0.0336138941347599,\n", " -0.08613722771406174,\n", " -0.0070341830141842365,\n", " 0.020017918199300766,\n", " 1.9721143827470743e-33,\n", " -0.040057308971881866,\n", " -0.08697956800460815,\n", " -0.10752362757921219,\n", " 0.046594224870204926,\n", " -0.02155344747006893,\n", " 0.06345468014478683,\n", " -0.0012436836259439588,\n", " 0.1024932935833931,\n", " -0.03840319439768791,\n", " 0.05636506900191307,\n", " 0.03455965965986252,\n", " -0.010708604007959366,\n", " 0.09041690826416016,\n", " -0.00387037405744195,\n", " 0.007843220606446266,\n", " -0.006417832802981138,\n", " 0.0793241634964943,\n", " -0.036810826510190964,\n", " -0.034556351602077484,\n", " 0.016351647675037384,\n", " -0.05868205055594444,\n", " 0.06102075055241585,\n", " 0.03319575637578964,\n", " 0.03555920720100403,\n", " 0.04192164167761803,\n", " 0.0274173766374588,\n", " -0.0012142949271947145,\n", " -0.08340507000684738,\n", " -0.027781514450907707,\n", " -0.002040418330579996,\n", " 0.05238410457968712,\n", " -0.011442624032497406,\n", " -0.15991191565990448,\n", " 0.10775579512119293,\n", " -0.05469131097197533,\n", " -0.05283842608332634,\n", " 0.07845485210418701,\n", " -0.008442937396466732,\n", " -0.036751579493284225,\n", " 0.09170155227184296,\n", " 0.06818988919258118,\n", " 0.06533976644277573,\n", " -0.027639612555503845,\n", " 0.02200944907963276,\n", " -0.023787008598446846,\n", " -0.05246135592460632,\n", " -0.020894961431622505,\n", " 0.03781576827168465,\n", " 0.05215656757354736,\n", " 0.0008395761833526194,\n", " 0.08176423609256744,\n", " -0.008409136906266212,\n", " 0.06646209955215454,\n", " -0.03922241926193237,\n", " 0.006130032241344452,\n", " 0.06710422784090042,\n", " -0.03348524495959282,\n", " -0.07163240760564804,\n", " -0.016983186826109886,\n", " -0.007842103019356728,\n", " 0.07378994673490524,\n", " 0.061417970806360245,\n", " -0.06878084689378738,\n", " 0.06929202377796173,\n", " 0.039635587483644485,\n", " -0.07560238242149353,\n", " -0.07143717259168625,\n", " 0.030205775052309036,\n", " -0.06733664125204086,\n", " 0.011503379791975021,\n", " 0.044333089143037796,\n", " -0.09573142975568771,\n", " -0.06681516766548157,\n", " -0.059916749596595764,\n", " 0.040238022804260254,\n", " -0.023708701133728027,\n", " 0.029659999534487724,\n", " 0.03539908677339554,\n", " -0.020465942099690437,\n", " -0.029972661286592484,\n", " 0.0921073853969574,\n", " 0.04007168486714363,\n", " -0.024441026151180267,\n", " 0.0575219951570034,\n", " 0.03931917995214462,\n", " 0.015297885984182358,\n", " -0.04066694900393486,\n", " -0.09090724587440491,\n", " 0.038153573870658875,\n", " 0.018748696893453598,\n", " -0.09823139011859894,\n", " -0.0007512288866564631,\n", " 0.02904438227415085,\n", " 0.05031117424368858,\n", " -0.04463545233011246,\n", " -1.5695729160825067e-08,\n", " -0.026139501482248306,\n", " -0.03394678980112076,\n", " -0.036586467176675797,\n", " -0.014450589194893837,\n", " -0.06972292065620422,\n", " -0.030430182814598083,\n", " 0.02459658682346344,\n", " -0.002665999112650752,\n", " -0.06651013344526291,\n", " 0.028640134260058403,\n", " 0.029509084299206734,\n", " 0.00048733700532466173,\n", " -0.08660132437944412,\n", " -0.07713275402784348,\n", " 0.0032888546120375395,\n", " -0.019061900675296783,\n", " 0.06681753695011139,\n", " -0.036887042224407196,\n", " -0.06013094633817673,\n", " 0.012280238792300224,\n", " 0.029498429968953133,\n", " 0.027579346671700478,\n", " 0.033414509147405624,\n", " -0.053730208426713943,\n", " 0.011133710853755474,\n", " 0.05815925821661949,\n", " 0.0523218996822834,\n", " 0.026335861533880234,\n", " 0.09067607671022415,\n", " -0.0071271429769694805,\n", " -0.02501191757619381,\n", " 0.09089876711368561,\n", " 0.020690225064754486,\n", " 0.004512567538768053,\n", " 0.01868719421327114,\n", " 0.05480153486132622,\n", " 0.08980021625757217,\n", " 0.031787630170583725,\n", " -0.01969604566693306,\n", " 0.009253603406250477,\n", " 0.006232365500181913,\n", " -0.04840949550271034,\n", " -0.0024302650708705187,\n", " 0.06945981830358505,\n", " 0.022277699783444405,\n", " 0.008216602727770805,\n", " -0.045067403465509415,\n", " -0.08695460110902786,\n", " 0.0018756430363282561,\n", " -0.08053696155548096,\n", " -0.06952144205570221,\n", " -0.039035994559526443,\n", " 0.08881521970033646,\n", " 0.09672388434410095,\n", " -0.06779682636260986,\n", " 0.013228203170001507,\n", " 0.047871217131614685,\n", " 0.02026950754225254,\n", " 0.02096926048398018,\n", " -0.015539136715233326,\n", " 0.07723184674978256,\n", " 0.05705316737294197,\n", " 0.07158508896827698,\n", " 0.07126484811306]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["text=\"this is atest documents\"\n", "query_result=embeddings.embed_query(text)\n", "query_result"]}, {"cell_type": "code", "execution_count": 8, "id": "e5b74847", "metadata": {}, "outputs": [{"data": {"text/plain": ["384"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["len(query_result)"]}, {"cell_type": "code", "execution_count": 10, "id": "39a82e70", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["e:\\agenticbatch2\\venv\\Lib\\site-packages\\huggingface_hub\\file_download.py:143: UserWarning: `huggingface_hub` cache-system uses symlinks by default to efficiently store duplicated files but your machine does not support them in C:\\Users\\<USER>\\.cache\\huggingface\\hub\\models--sentence-transformers--all-mpnet-base-v2. Caching files will still work but in a degraded version that might require more space on your disk. This warning can be disabled by setting the `HF_HUB_DISABLE_SYMLINKS_WARNING` environment variable. For more details, see https://huggingface.co/docs/huggingface_hub/how-to-cache#limitations.\n", "To support symlinks on Windows, you either need to activate Developer Mode or to run Python as an administrator. In order to activate developer mode, see this article: https://docs.microsoft.com/en-us/windows/apps/get-started/enable-your-device-for-development\n", "  warnings.warn(message)\n", "Xet Storage is enabled for this repo, but the 'hf_xet' package is not installed. Falling back to regular HTTP download. For better performance, install the package with: `pip install huggingface_hub[hf_xet]` or `pip install hf_xet`\n"]}], "source": ["from langchain_huggingface import HuggingFaceEmbeddings\n", "\n", "embeddings = HuggingFaceEmbeddings(model_name=\"sentence-transformers/all-mpnet-base-v2\")"]}, {"cell_type": "code", "execution_count": 11, "id": "e4f13c89", "metadata": {}, "outputs": [{"data": {"text/plain": ["768"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["vectors=embeddings.embed_query(\"Hello, world!\")\n", "len(vectors)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.0"}}, "nbformat": 4, "nbformat_minor": 5}