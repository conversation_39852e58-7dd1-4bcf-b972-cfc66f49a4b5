# 📁 Assignment 01: Environment Setup

## 🎯 Learning Objectives
By the end of this assignment, you will:
- Understand what conda environments are and why we use them
- Create and activate a conda environment
- Install required packages for LangChain
- Set up environment variables for API keys

## 🤔 Problem Statement
**The Problem**: You want to build AI applications, but your computer doesn't have the right tools installed.

**Real-world analogy**: Imagine you want to bake a cake, but your kitchen has no oven, mixing bowls, or ingredients. You need to set up your kitchen first!

## 🧠 What is a Conda Environment?

**Simple explanation**: A conda environment is like a separate workspace on your computer where you can install specific versions of software without affecting other projects.

**Why we need it**:
- Different projects need different versions of packages
- Keeps your projects organized and separate
- Easy to share your setup with others
- If something breaks, it doesn't affect other projects

**Real-world analogy**: Like having separate toolboxes for different hobbies - one for woodworking, one for electronics, one for cooking.

## 📋 Step-by-Step Instructions

### Step 1: Check if Conda is Installed
```bash
conda --version
```
**What this does**: Checks if conda is installed on your computer
**Expected output**: Something like `conda 4.12.0`

### Step 2: List Existing Environments
```bash
conda env list
```
**What this does**: Shows all conda environments on your computer
**Why we do this**: To see what environments already exist

### Step 3: Create a New Environment
```bash
conda create -n langchain_learning python=3.10
```
**What this does**: Creates a new environment called "langchain_learning" with Python 3.10
**Why Python 3.10**: It's stable and works well with all LangChain packages

### Step 4: Activate the Environment
```bash
conda activate langchain_learning
```
**What this does**: Switches to your new environment
**How to know it worked**: Your terminal prompt will show `(langchain_learning)`

### Step 5: Install Required Packages
```bash
pip install python-dotenv langchain langchain-openai langchain-groq langchain-core langchain-community
```
**What each package does**:
- `python-dotenv`: Loads environment variables from .env files
- `langchain`: Main LangChain framework
- `langchain-openai`: Connects to OpenAI models (ChatGPT)
- `langchain-groq`: Connects to Groq models (fast AI)
- `langchain-core`: Core LangChain functionality
- `langchain-community`: Community-contributed components

### Step 6: Install Additional Packages
```bash
pip install pydantic jupyter notebook
```
**What these do**:
- `pydantic`: Data validation and parsing
- `jupyter`: For running notebook files
- `notebook`: Jupyter notebook interface

## 🔑 Setting Up Environment Variables

### What are Environment Variables?
**Simple explanation**: Secret information (like passwords) that your programs can access without you typing them every time.

**Why we use them**:
- Keep API keys secret and secure
- Don't accidentally share secrets in code
- Easy to change without modifying code

### Step 1: Copy the .env File
```bash
cp ../../2-langchain/.env .
```
**What this does**: Copies the environment file with your API keys

### Step 2: Verify the .env File
```bash
cat .env
```
**What you should see**:
```
OPENAI_API_KEY="your-openai-key-here"
LANGCHAIN_API_KEY="your-langchain-key-here"
LANGCHAIN_PROJECT="Agentic2.0"
LANGCHAIN_TRACING_V2="true"
GROQ_API_KEY="your-groq-key-here"
```

## ✅ Verification Steps

Run this test to make sure everything is working:

```python
# Test 1: Check if packages are installed
try:
    import langchain
    import langchain_openai
    import langchain_groq
    from dotenv import load_dotenv
    print("✅ All packages installed successfully!")
except ImportError as e:
    print(f"❌ Missing package: {e}")

# Test 2: Check if environment variables load
load_dotenv()
import os
if os.getenv("OPENAI_API_KEY"):
    print("✅ Environment variables loaded!")
else:
    print("❌ Environment variables not found!")
```

## 🎯 Assignment Tasks

1. **Create the environment** following the steps above
2. **Install all packages** and verify they work
3. **Set up the .env file** with your API keys
4. **Run the verification test** and make sure everything passes
5. **Take a screenshot** of your successful verification

## 🚀 Next Steps

Once you complete this assignment:
- Your environment will be ready for AI development
- You'll have all necessary packages installed
- Your API keys will be securely configured
- You can move to Assignment 02!

## 🆘 Troubleshooting

**Problem**: `conda: command not found`
**Solution**: Install Anaconda or Miniconda first

**Problem**: Package installation fails
**Solution**: Make sure you activated the environment first

**Problem**: Environment variables not loading
**Solution**: Make sure the .env file is in the correct directory

---
**🎉 Congratulations!** Once you complete this, you'll have a professional AI development environment set up!
