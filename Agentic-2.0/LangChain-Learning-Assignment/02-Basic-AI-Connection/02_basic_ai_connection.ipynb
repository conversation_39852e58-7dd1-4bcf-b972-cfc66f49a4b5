{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 📚 Assignment 02: Basic AI Connection\n", "\n", "## 🎯 What We're Going to Do\n", "In this notebook, we'll learn how to connect to different AI models and get responses from them.\n", "\n", "## 🧠 Learning Goals\n", "- Connect to OpenAI models\n", "- Connect to Groq models\n", "- Compare different AI models\n", "- Understand model parameters\n", "- Handle AI responses"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1: Setup Environment\n", "\n", "**What we're doing**: Loading our environment and importing necessary packages\n", "\n", "**Why this matters**: We need our API keys and LangChain tools to work with AI\n", "\n", "**What to expect**: If successful, you'll see \"✅ Environment ready!\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Step 1: Import everything we need\n", "import os\n", "from dotenv import load_dotenv\n", "from langchain_openai import ChatOpenAI\n", "from langchain_groq import ChatGroq\n", "\n", "# Load our API keys\n", "load_dotenv()\n", "\n", "# Set environment variables for this session\n", "os.environ[\"OPENAI_API_KEY\"] = os.getenv(\"OPENAI_API_KEY\")\n", "os.environ[\"GROQ_API_KEY\"] = os.getenv(\"GROQ_API_KEY\")\n", "os.environ[\"LANGCHAIN_API_KEY\"] = os.getenv(\"LANGCHAIN_API_KEY\")\n", "os.environ[\"LANGCHAIN_PROJECT\"] = os.getenv(\"LANGCHAIN_PROJECT\")\n", "os.environ[\"LANGCHAIN_TRACING_V2\"] = \"true\"\n", "\n", "\n", "print(\"✅ Environment ready!\")\n", "print(\"📊 LangChain tracing enabled - you can monitor your AI calls\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2: Your First AI Connection\n", "\n", "**What we're doing**: Creating our first connection to an AI model\n", "\n", "**Why this matters**: This is like getting a phone number to call an AI assistant\n", "\n", "**How it works**: We create a 'model object' that knows how to talk to the AI service"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Step 2: Create your first AI model connection\n", "# Think of this like getting a phone number for a smart assistant\n", "\n", "openai_model = ChatOpenAI(\n", "    model=\"gpt-3.5-turbo\",  # Which AI brain to use\n", "    temperature=0.7        # How creative should it be? (0 = boring, 1 = very creative)\n", ")\n", "\n", "print(\"✅ OpenAI model created!\")\n", "print(f\"📱 Model name: {openai_model.model_name}\")\n", "print(f\"🌡️ Temperature: {openai_model.temperature}\")\n", "print(f\"🔑 Has API key: {'Yes' if openai_model.openai_api_key else 'No'}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 3: Your First AI Conversation\n", "\n", "**What we're doing**: Asking the AI a question and getting a response\n", "\n", "**Why this matters**: This is the basic building block of all AI applications\n", "\n", "**How it works**: We use `.invoke()` to send a message and get a response"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Step 3: Have your first conversation with <PERSON>\n", "# This is like making your first phone call!\n", "\n", "question = \"What is artificial intelligence in simple terms?\"\n", "print(f\"🙋 You asked: {question}\")\n", "print(\"⏳ Waiting for AI response...\")\n", "\n", "# Send the question to AI and get response\n", "response = openai_model.invoke(question)\n", "\n", "print(\"\\n🤖 AI Response:\")\n", "print(response.content)\n", "print(f\"\\n📊 Response length: {len(response.content)} characters\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 4: Understanding the Response Object\n", "\n", "**What we're doing**: Looking at what the AI actually returns\n", "\n", "**Why this matters**: Understanding the response helps us use it better\n", "\n", "**What's inside**: The response contains the text, metadata, and other useful information"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Step 4: Explore what's inside an AI response\n", "# Think of this like examining a letter you received\n", "\n", "print(\"🔍 Let's examine the AI response object:\")\n", "print(f\"📝 Type: {type(response)}\")\n", "print(f\"📄 Content: {response.content[:100]}...\")  # First 100 characters\n", "print(f\"🏷️ Response metadata: {response.response_metadata}\")\n", "\n", "# The most important part is usually the content\n", "print(\"\\n💡 The main text is in: response.content\")\n", "print(\"💡 This is what you'll use most of the time!\")"]}], "metadata": {"kernelspec": {"display_name": "langchain_learning", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 2}