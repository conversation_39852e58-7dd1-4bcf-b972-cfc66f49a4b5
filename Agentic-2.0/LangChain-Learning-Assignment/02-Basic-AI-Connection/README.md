# 📁 Assignment 02: Basic AI Connection

## 🎯 Learning Objectives
By the end of this assignment, you will:
- Understand what AI models are and how they work
- Learn the difference between different AI providers (OpenAI vs Groq)
- Connect to AI models and get responses
- Understand how to choose the right model for your task

## 🤔 Problem Statement
**The Problem**: You have a working environment, but you don't know how to actually talk to AI models.

**Real-world analogy**: You have a phone (environment), but you don't know how to call different people (AI models) or what to say to them.

## 🧠 What are AI Models?

**Simple explanation**: AI models are like very smart assistants that can understand and respond to text. Each model has different strengths.

**Think of it like different experts**:
- **OpenAI GPT**: Like a university professor - very knowledgeable, thoughtful responses
- **Groq Models**: Like a quick-thinking student - fast responses, good for simple tasks

## 🏢 AI Providers We'll Use

### 1. OpenAI
**What it is**: The company that made ChatGPT
**Models we'll use**: 
- `gpt-3.5-turbo`: Fast and affordable
- `gpt-4`: More intelligent but slower
- `o1-mini`: Good for reasoning tasks

**When to use**: When you need high-quality, thoughtful responses

### 2. Groq
**What it is**: A company that makes AI models run very fast
**Models we'll use**:
- `gemma2-9b-it`: Fast general-purpose model
- `qwen-qwq-32b`: Good for complex reasoning

**When to use**: When you need quick responses or are testing

## 📋 Step-by-Step Learning Path

### Step 1: Understanding the Connection Process
```python
# This is like dialing a phone number
model = ChatOpenAI(model="gpt-3.5-turbo")

# This is like talking to the person
response = model.invoke("Hello, how are you?")
```

**What happens**:
1. Your code connects to OpenAI's servers
2. Sends your message
3. AI processes it and sends back a response
4. You receive the response

### Step 2: Different Ways to Connect
We'll learn 3 different connection methods:
1. **Direct connection** - Simple one-time use
2. **Reusable connection** - Create once, use many times  
3. **Connection with settings** - Customize how AI behaves

## 🎯 What You'll Build

By the end of this assignment, you'll create:
1. **AI Chatbot** - Simple conversation with AI
2. **Model Comparison Tool** - Compare responses from different models
3. **Smart Assistant** - AI that remembers context

## 🚀 Why This Matters

**Real-world applications**:
- Customer service chatbots
- Content writing assistants
- Code helpers
- Language translators
- Question-answering systems

## ✅ Success Criteria

You'll know you've mastered this when you can:
- ✅ Connect to any AI model
- ✅ Get responses from AI
- ✅ Choose the right model for your task
- ✅ Handle errors when connections fail
- ✅ Compare different AI models

## 🆘 Common Issues & Solutions

**Problem**: "API key not found"
**Solution**: Check your .env file has the correct keys

**Problem**: "Model not available"
**Solution**: Check the model name spelling

**Problem**: "Rate limit exceeded"
**Solution**: Wait a moment and try again

---
**🎉 Ready to start?** Open the notebook `02_basic_ai_connection.ipynb` to begin!
