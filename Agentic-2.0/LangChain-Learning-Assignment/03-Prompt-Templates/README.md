# 📁 Assignment 03: Prompt Templates

## 🎯 Learning Objectives
By the end of this assignment, you will:
- Understand what prompt templates are and why they're important
- Learn to create reusable prompt templates
- Master different types of prompts (system, user, assistant)
- Build consistent AI interactions
- Create professional AI applications with structured prompts

## 🤔 Problem Statement
**The Problem**: Every time you want to ask the AI something, you have to write the full instructions from scratch. This is time-consuming and leads to inconsistent results.

**Real-world analogy**: It's like writing a formal letter every time you want to ask someone a question, instead of having a template where you just fill in the specific details.

## 🧠 What Are Prompt Templates?

**Simple explanation**: Prompt templates are like form letters for AI. You create the structure once, then just fill in the specific details each time you use it.

**Example**:
- **Without template**: "Hi <PERSON>, please act as an expert chef and tell me how to cook pasta. Make sure to be detailed and helpful."
- **With template**: "You are an expert {profession}. Please explain how to {task}. Be {style} in your response."

## 🎭 Types of Messages in AI Conversations

### 1. System Message
**What it is**: Instructions that tell the AI how to behave
**Purpose**: Sets the AI's personality, role, and behavior rules
**Example**: "You are a helpful cooking assistant. Always provide step-by-step instructions."

### 2. User Message  
**What it is**: The actual question or request from the user
**Purpose**: Contains the specific information the user wants
**Example**: "How do I make chocolate chip cookies?"

### 3. Assistant Message
**What it is**: Previous AI responses (for conversation context)
**Purpose**: Helps maintain conversation flow and context
**Example**: Used when you want to continue a previous conversation

## 🏗️ Building Blocks of Good Prompts

### 1. Role Definition
Tell the AI what role to play:
- "You are an expert teacher..."
- "Act as a professional writer..."
- "You are a helpful customer service agent..."

### 2. Context Setting
Provide background information:
- "The user is a beginner..."
- "This is for a business presentation..."
- "The audience is children..."

### 3. Task Specification
Clearly state what you want:
- "Explain the concept of..."
- "Write a summary of..."
- "Create a list of..."

### 4. Output Format
Specify how you want the response:
- "Respond in bullet points"
- "Use simple language"
- "Provide examples"

## 📋 Types of Prompt Templates

### 1. Simple Template
```python
template = "Explain {topic} in simple terms."
```

### 2. Chat Template
```python
template = ChatPromptTemplate.from_messages([
    ("system", "You are a helpful assistant."),
    ("user", "{question}")
])
```

### 3. Complex Template
```python
template = ChatPromptTemplate.from_messages([
    ("system", "You are an expert {role}. Your audience is {audience}."),
    ("user", "Please {task} about {topic}. Use {style} language.")
])
```

## 🎯 Why Use Prompt Templates?

### 1. Consistency
- Same format every time
- Predictable results
- Professional appearance

### 2. Reusability
- Write once, use many times
- Easy to modify
- Share with team members

### 3. Maintainability
- Update in one place
- Version control
- Easy debugging

### 4. Efficiency
- Faster development
- Less typing
- Fewer errors

## 🛠️ How Prompt Templates Work

### Step 1: Define the Template
```python
template = "You are a {role}. Help the user with {task}."
```

### Step 2: Fill in Variables
```python
filled_prompt = template.format(role="teacher", task="math problems")
```

### Step 3: Send to AI
```python
response = model.invoke(filled_prompt)
```

## 📊 Template Best Practices

### ✅ Do This:
- Use clear, specific instructions
- Include examples when helpful
- Define the AI's role clearly
- Specify output format
- Use consistent variable names

### ❌ Avoid This:
- Vague or ambiguous instructions
- Too many variables in one template
- Conflicting instructions
- Overly complex templates
- Missing context

## 🧪 Real-World Examples

### Example 1: Customer Service Bot
```python
template = ChatPromptTemplate.from_messages([
    ("system", "You are a friendly customer service representative for {company}. Always be polite and helpful."),
    ("user", "{customer_question}")
])
```

### Example 2: Code Explanation Tool
```python
template = ChatPromptTemplate.from_messages([
    ("system", "You are a programming tutor. Explain code in simple terms for {skill_level} programmers."),
    ("user", "Explain this code: {code}")
])
```

### Example 3: Content Creator
```python
template = ChatPromptTemplate.from_messages([
    ("system", "You are a creative writer specializing in {content_type}. Write for {target_audience}."),
    ("user", "Create content about {topic} with {tone} tone.")
])
```

## 🎯 Assignment Tasks

### Task 1: Create Basic Templates
Create templates for:
1. A math tutor
2. A cooking assistant  
3. A travel guide
4. A fitness coach

### Task 2: Test Template Variations
Use the same template with different variables and compare results

### Task 3: Build a Multi-Turn Conversation
Create templates that can handle back-and-forth conversations

### Task 4: Error-Proof Templates
Create templates that handle missing or invalid inputs gracefully

## 🏆 Success Criteria

You'll know you're successful when you can:
- ✅ Create reusable prompt templates
- ✅ Use variables effectively in templates
- ✅ Build consistent AI interactions
- ✅ Handle different message types (system, user, assistant)
- ✅ Create professional AI applications

## 🚀 Real-World Applications

After this assignment, you'll be able to:
- Build chatbots with consistent personalities
- Create AI tools for specific industries
- Develop scalable AI applications
- Maintain and update AI behavior easily

## 🆘 Common Issues and Solutions

**Problem**: "AI responses are inconsistent"
**Solution**: Use more specific system messages and examples

**Problem**: "Template variables not working"
**Solution**: Check variable names match exactly

**Problem**: "AI ignoring instructions"
**Solution**: Make instructions clearer and more specific

**Problem**: "Template too complex"
**Solution**: Break into smaller, simpler templates

---
**🎯 Ready to start?** Open the Jupyter notebook `03_prompt_templates.ipynb` to begin!
