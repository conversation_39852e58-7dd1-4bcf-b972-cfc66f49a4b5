{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🤖 Step 2: AI Model Integration Review\n", "\n", "## 🎯 What We're Building\n", "A professional AI model manager that can switch between different AI services and optimize performance.\n", "\n", "## 🧠 Why This Step Matters\n", "- **Flexibility**: Switch between models based on task requirements\n", "- **Cost Optimization**: Use cheaper models for simple tasks\n", "- **Performance**: Use faster models when speed matters\n", "- **Reliability**: Fallback options when one service is down\n", "\n", "## 🔄 Connection to Previous Learning\n", "This builds on **Assignment 02** but adds professional model management features."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2.1: Understanding Model Characteristics\n", "\n", "**What we're doing**: Learning the strengths and weaknesses of different AI models\n", "\n", "**Why this matters**: Choosing the right model for each task improves results and reduces costs\n", "\n", "**Real-world analogy**: Like choosing the right tool for each job - you wouldn't use a hammer to cut paper"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Model characteristics database\n", "import pandas as pd\n", "from dataclasses import dataclass\n", "from typing import Dict, List\n", "\n", "@dataclass\n", "class ModelInfo:\n", "    \"\"\"Information about an AI model\"\"\"\n", "    name: str\n", "    provider: str\n", "    speed: str  # \"fast\", \"medium\", \"slow\"\n", "    cost: str   # \"low\", \"medium\", \"high\"\n", "    quality: str # \"good\", \"very good\", \"excellent\"\n", "    best_for: List[str]\n", "    context_length: int  # Maximum tokens\n", "    \n", "# Define our available models\n", "AVAILABLE_MODELS = {\n", "    \"gpt-3.5-turbo\": ModelInfo(\n", "        name=\"gpt-3.5-turbo\",\n", "        provider=\"OpenAI\",\n", "        speed=\"medium\",\n", "        cost=\"medium\",\n", "        quality=\"very good\",\n", "        best_for=[\"general chat\", \"simple analysis\", \"quick responses\"],\n", "        context_length=4096\n", "    ),\n", "    \"gpt-4\": ModelInfo(\n", "        name=\"gpt-4\",\n", "        provider=\"OpenAI\",\n", "        speed=\"slow\",\n", "        cost=\"high\",\n", "        quality=\"excellent\",\n", "        best_for=[\"complex reasoning\", \"creative writing\", \"detailed analysis\"],\n", "        context_length=8192\n", "    ),\n", "    \"gemma2-9b-it\": ModelInfo(\n", "        name=\"gemma2-9b-it\",\n", "        provider=\"Groq\",\n", "        speed=\"fast\",\n", "        cost=\"low\",\n", "        quality=\"good\",\n", "        best_for=[\"quick responses\", \"simple tasks\", \"high volume\"],\n", "        context_length=8192\n", "    ),\n", "    \"llama3-70b-8192\": ModelInfo(\n", "        name=\"llama3-70b-8192\",\n", "        provider=\"Groq\",\n", "        speed=\"fast\",\n", "        cost=\"low\",\n", "        quality=\"very good\",\n", "        best_for=[\"code generation\", \"technical tasks\", \"reasoning\"],\n", "        context_length=8192\n", "    )\n", "}\n", "\n", "# Display model comparison\n", "print(\"🤖 Available AI Models Comparison\")\n", "print(\"=\" * 60)\n", "\n", "# Create comparison table\n", "model_data = []\n", "for model_id, info in AVAILABLE_MODELS.items():\n", "    model_data.append({\n", "        \"Model\": info.name,\n", "        \"Provider\": info.provider,\n", "        \"Speed\": info.speed,\n", "        \"Cost\": info.cost,\n", "        \"Quality\": info.quality,\n", "        \"Context Length\": info.context_length,\n", "        \"Best For\": \", \".join(info.best_for[:2])  # Show first 2 use cases\n", "    })\n", "\n", "df = pd.DataFrame(model_data)\n", "print(df.to_string(index=False))\n", "\n", "print(\"\\n💡 Model Selection Guidelines:\")\n", "print(\"  🚀 Fast + Cheap: Use Groq models for simple tasks\")\n", "print(\"  ⚖️ Balanced: Use GPT-3.5-turbo for general purposes\")\n", "print(\"  🧠 Best Quality: Use GPT-4 for complex reasoning\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2.2: Building a Model Manager Class\n", "\n", "**What we're doing**: Creating a professional class to manage multiple AI models\n", "\n", "**Why we need this**: \n", "- **Centralized Control**: One place to manage all models\n", "- **Easy Switching**: Change models without changing application code\n", "- **Error <PERSON>**: Graceful fallbacks when models fail\n", "- **Performance Tracking**: Monitor which models work best\n", "\n", "**Design Pattern**: This is called the \"Strategy Pattern\" - we can switch strategies (models) at runtime"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Professional AI Model Manager\n", "import os\n", "import time\n", "from typing import Optional, Dict, Any\n", "from dotenv import load_dotenv\n", "from langchain_openai import ChatOpenAI\n", "from langchain_groq import ChatGroq\n", "\n", "class AIModelManager:\n", "    \"\"\"\n", "    Professional AI Model Manager\n", "    \n", "    This class manages multiple AI models and provides:\n", "    - Easy model switching\n", "    - Automatic fallbacks\n", "    - Performance monitoring\n", "    - Error handling\n", "    \"\"\"\n", "    \n", "    def __init__(self):\n", "        \"\"\"Initialize the model manager\"\"\"\n", "        # Load environment variables\n", "        load_dotenv()\n", "        \n", "        # Set up environment variables\n", "        self._setup_environment()\n", "        \n", "        # Initialize model storage\n", "        self.models: Dict[str, Any] = {}\n", "        self.current_model: Optional[str] = None\n", "        self.performance_stats: Dict[str, Dict] = {}\n", "        \n", "        # Initialize available models\n", "        self._initialize_models()\n", "        \n", "        print(\"✅ AI Model Manager initialized\")\n", "        print(f\"📊 Available models: {list(self.models.keys())}\")\n", "    \n", "    def _setup_environment(self):\n", "        \"\"\"Set up environment variables for AI services\"\"\"\n", "        os.environ[\"OPENAI_API_KEY\"] = os.getenv(\"OPENAI_API_KEY\", \"\")\n", "        os.environ[\"GROQ_API_KEY\"] = os.getenv(\"GROQ_API_KEY\", \"\")\n", "        os.environ[\"LANGCHAIN_API_KEY\"] = os.getenv(\"LANGCHAIN_API_KEY\", \"\")\n", "        os.environ[\"LANGCHAIN_PROJECT\"] = os.getenv(\"LANGCHAIN_PROJECT\", \"AI-Application\")\n", "        os.environ[\"LANGCHAIN_TRACING_V2\"] = \"true\"\n", "    \n", "    def _initialize_models(self):\n", "        \"\"\"Initialize all available AI models\"\"\"\n", "        print(\"🔧 Initializing AI models...\")\n", "        \n", "        # OpenAI Models\n", "        try:\n", "            self.models[\"gpt-3.5-turbo\"] = ChatOpenAI(\n", "                model=\"gpt-3.5-turbo\",\n", "                temperature=0.7,\n", "                max_tokens=1000\n", "            )\n", "            print(\"  ✅ GPT-3.5-turbo initialized\")\n", "        except Exception as e:\n", "            print(f\"  ❌ GPT-3.5-turbo failed: {e}\")\n", "        \n", "        try:\n", "            self.models[\"gpt-4\"] = ChatOpenAI(\n", "                model=\"gpt-4\",\n", "                temperature=0.7,\n", "                max_tokens=1000\n", "            )\n", "            print(\"  ✅ GPT-4 initialized\")\n", "        except Exception as e:\n", "            print(f\"  ❌ GPT-4 failed: {e}\")\n", "        \n", "        # Groq Models\n", "        try:\n", "            self.models[\"gemma2-9b-it\"] = ChatGroq(\n", "                model=\"gemma2-9b-it\",\n", "                temperature=0.7,\n", "                max_tokens=1000\n", "            )\n", "            print(\"  ✅ Gemma2-9B initialized\")\n", "        except Exception as e:\n", "            print(f\"  ❌ Gemma2-9B failed: {e}\")\n", "        \n", "        try:\n", "            self.models[\"llama3-70b-8192\"] = ChatGroq(\n", "                model=\"llama3-70b-8192\",\n", "                temperature=0.7,\n", "                max_tokens=1000\n", "            )\n", "            print(\"  ✅ Llama3-70B initialized\")\n", "        except Exception as e:\n", "            print(f\"  ❌ Llama3-70B failed: {e}\")\n", "        \n", "        # Set default model\n", "        if self.models:\n", "            self.current_model = list(self.models.keys())[0]\n", "            print(f\"🎯 Default model set to: {self.current_model}\")\n", "        else:\n", "            print(\"❌ No models available! Check your API keys.\")\n", "\n", "# Create and test the model manager\n", "print(\"🚀 Creating AI Model Manager...\")\n", "model_manager = AIModelManager()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2.3: Model Selection and Switching\n", "\n", "**What we're doing**: Adding intelligent model selection based on task requirements\n", "\n", "**Why this is powerful**: Automatically choose the best model for each task\n", "\n", "**How it works**: We analyze the task and select the optimal model based on requirements"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🧪 Testing Model Selection...\n", "========================================\n"]}, {"ename": "AttributeError", "evalue": "'AIModelManager' object has no attribute 'recommend_model'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[26], line 90\u001b[0m\n\u001b[1;32m     82\u001b[0m scenarios \u001b[38;5;241m=\u001b[39m [\n\u001b[1;32m     83\u001b[0m     (\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124msimple\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mspeed\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mQuick FAQ response\u001b[39m\u001b[38;5;124m\"\u001b[39m),\n\u001b[1;32m     84\u001b[0m     (\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcomplex\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mquality\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mDetailed analysis\u001b[39m\u001b[38;5;124m\"\u001b[39m),\n\u001b[1;32m     85\u001b[0m     (\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcreative\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mbalanced\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mStory writing\u001b[39m\u001b[38;5;124m\"\u001b[39m),\n\u001b[1;32m     86\u001b[0m     (\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtechnical\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mspeed\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mCode explanation\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m     87\u001b[0m ]\n\u001b[1;32m     89\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m task_type, priority, description \u001b[38;5;129;01min\u001b[39;00m scenarios:\n\u001b[0;32m---> 90\u001b[0m     recommended \u001b[38;5;241m=\u001b[39m \u001b[43mmodel_manager\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrecommend_model\u001b[49m(task_type, priority)\n\u001b[1;32m     91\u001b[0m     \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m📋 Task: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mdescription\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m     92\u001b[0m     \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m   Type: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mtask_type\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m, Priority: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mpriority\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n", "\u001b[0;31mAttributeError\u001b[0m: 'AIModelManager' object has no attribute 'recommend_model'"]}], "source": ["# Add model selection methods to our manager\n", "class AIModelManager(AIModelManager):  # Extend our existing class\n", "    \n", "    def get_available_models(self) -> List[str]:\n", "        \"\"\"Get list of available model names\"\"\"\n", "        return list(self.models.keys())\n", "    \n", "    def set_model(self, model_name: str) -> bool:\n", "        \"\"\"Set the current active model\"\"\"\n", "        if model_name in self.models:\n", "            self.current_model = model_name\n", "            print(f\"✅ Switched to model: {model_name}\")\n", "            return True\n", "        else:\n", "            print(f\"❌ Model '{model_name}' not available\")\n", "            print(f\"Available models: {self.get_available_models()}\")\n", "            return False\n", "    \n", "    def get_current_model(self) -> Optional[str]:\n", "        \"\"\"Get the currently active model name\"\"\"\n", "        return self.current_model\n", "    \n", "    def recommend_model(self, task_type: str, priority: str = \"balanced\") -> str:\n", "        \"\"\"\n", "        Recommend the best model for a specific task\n", "        \n", "        Args:\n", "            task_type: Type of task (\"simple\", \"complex\", \"creative\", \"technical\")\n", "            priority: What to optimize for (\"speed\", \"quality\", \"cost\", \"balanced\")\n", "        \n", "        Returns:\n", "            Recommended model name\n", "        \"\"\"\n", "        recommendations = {\n", "            \"simple\": {\n", "                \"speed\": \"gemma2-9b-it\",\n", "                \"quality\": \"gpt-3.5-turbo\",\n", "                \"cost\": \"gemma2-9b-it\",\n", "                \"balanced\": \"gemma2-9b-it\"\n", "            },\n", "            \"complex\": {\n", "                \"speed\": \"llama3-70b-8192\",\n", "                \"quality\": \"gpt-4\",\n", "                \"cost\": \"llama3-70b-8192\",\n", "                \"balanced\": \"gpt-3.5-turbo\"\n", "            },\n", "            \"creative\": {\n", "                \"speed\": \"llama3-70b-8192\",\n", "                \"quality\": \"gpt-4\",\n", "                \"cost\": \"llama3-70b-8192\",\n", "                \"balanced\": \"gpt-3.5-turbo\"\n", "            },\n", "            \"technical\": {\n", "                \"speed\": \"llama3-70b-8192\",\n", "                \"quality\": \"gpt-4\",\n", "                \"cost\": \"llama3-70b-8192\",\n", "                \"balanced\": \"llama3-70b-8192\"\n", "            }\n", "        }\n", "        \n", "        recommended = recommendations.get(task_type, {}).get(priority, \"gpt-3.5-turbo\")\n", "        \n", "        # Check if recommended model is available\n", "        if recommended in self.models:\n", "            return recommended\n", "        else:\n", "            # Fallback to first available model\n", "            return self.get_available_models()[0] if self.models else None\n", "    \n", "    def auto_select_model(self, task_type: str, priority: str = \"balanced\") -> bool:\n", "        \"\"\"Automatically select and switch to the best model for a task\"\"\"\n", "        recommended = self.recommend_model(task_type, priority)\n", "        if recommended:\n", "            return self.set_model(recommended)\n", "        return False\n", "\n", "# Test model selection\n", "print(\"🧪 Testing Model Selection...\")\n", "print(\"=\" * 40)\n", "\n", "# Test different scenarios\n", "scenarios = [\n", "    (\"simple\", \"speed\", \"Quick FAQ response\"),\n", "    (\"complex\", \"quality\", \"Detailed analysis\"),\n", "    (\"creative\", \"balanced\", \"Story writing\"),\n", "    (\"technical\", \"speed\", \"Code explanation\")\n", "]\n", "\n", "for task_type, priority, description in scenarios:\n", "    recommended = model_manager.recommend_model(task_type, priority)\n", "    print(f\"📋 Task: {description}\")\n", "    print(f\"   Type: {task_type}, Priority: {priority}\")\n", "    print(f\"   Recommended: {recommended}\")\n", "    print()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2.4: Performance Monitoring and Testing\n", "\n", "**What we're doing**: Adding performance tracking to compare models\n", "\n", "**Why this matters**: Data-driven decisions about which models work best\n", "\n", "**What we track**: Response time, success rate, token usage, user satisfaction"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Add performance monitoring to our model manager\n", "import time\n", "from datetime import datetime\n", "\n", "class AIModelManager(AIModelManager):  # Extend our existing class\n", "    \n", "    def invoke_with_monitoring(self, message: str, model_name: Optional[str] = None) -> Dict[str, Any]:\n", "        \"\"\"\n", "        Invoke a model with performance monitoring\n", "        \n", "        Args:\n", "            message: The message to send to the AI\n", "            model_name: Specific model to use (optional)\n", "        \n", "        Returns:\n", "            Dictionary with response and performance metrics\n", "        \"\"\"\n", "        # Use specified model or current model\n", "        model_to_use = model_name or self.current_model\n", "        \n", "        if not model_to_use or model_to_use not in self.models:\n", "            return {\n", "                \"success\": <PERSON><PERSON><PERSON>,\n", "                \"error\": f\"Model '{model_to_use}' not available\",\n", "                \"response\": None,\n", "                \"metrics\": {}\n", "            }\n", "        \n", "        # Initialize performance stats for this model if needed\n", "        if model_to_use not in self.performance_stats:\n", "            self.performance_stats[model_to_use] = {\n", "                \"total_requests\": 0,\n", "                \"successful_requests\": 0,\n", "                \"total_response_time\": 0,\n", "                \"average_response_time\": 0,\n", "                \"last_used\": None\n", "            }\n", "        \n", "        # Start timing\n", "        start_time = time.time()\n", "        \n", "        try:\n", "            # Get the model and invoke it\n", "            model = self.models[model_to_use]\n", "            response = model.invoke(message)\n", "            \n", "            # Calculate response time\n", "            response_time = time.time() - start_time\n", "            \n", "            # Update performance stats\n", "            stats = self.performance_stats[model_to_use]\n", "            stats[\"total_requests\"] += 1\n", "            stats[\"successful_requests\"] += 1\n", "            stats[\"total_response_time\"] += response_time\n", "            stats[\"average_response_time\"] = stats[\"total_response_time\"] / stats[\"total_requests\"]\n", "            stats[\"last_used\"] = datetime.now().isoformat()\n", "            \n", "            return {\n", "                \"success\": True,\n", "                \"response\": response.content,\n", "                \"model_used\": model_to_use,\n", "                \"metrics\": {\n", "                    \"response_time\": response_time,\n", "                    \"response_length\": len(response.content),\n", "                    \"timestamp\": datetime.now().isoformat()\n", "                }\n", "            }\n", "            \n", "        except Exception as e:\n", "            # Update failure stats\n", "            response_time = time.time() - start_time\n", "            stats = self.performance_stats[model_to_use]\n", "            stats[\"total_requests\"] += 1\n", "            stats[\"total_response_time\"] += response_time\n", "            stats[\"average_response_time\"] = stats[\"total_response_time\"] / stats[\"total_requests\"]\n", "            \n", "            return {\n", "                \"success\": <PERSON><PERSON><PERSON>,\n", "                \"error\": str(e),\n", "                \"response\": None,\n", "                \"model_used\": model_to_use,\n", "                \"metrics\": {\n", "                    \"response_time\": response_time,\n", "                    \"timestamp\": datetime.now().isoformat()\n", "                }\n", "            }\n", "    \n", "    def get_performance_report(self) -> str:\n", "        \"\"\"Generate a performance report for all models\"\"\"\n", "        if not self.performance_stats:\n", "            return \"No performance data available yet.\"\n", "        \n", "        report = \"📊 Model Performance Report\\n\"\n", "        report += \"=\" * 50 + \"\\n\\n\"\n", "        \n", "        for model_name, stats in self.performance_stats.items():\n", "            success_rate = (stats[\"successful_requests\"] / stats[\"total_requests\"] * 100) if stats[\"total_requests\"] > 0 else 0\n", "            \n", "            report += f\"🤖 {model_name}:\\n\"\n", "            report += f\"   Total Requests: {stats['total_requests']}\\n\"\n", "            report += f\"   Success Rate: {success_rate:.1f}%\\n\"\n", "            report += f\"   Avg Response Time: {stats['average_response_time']:.2f}s\\n\"\n", "            report += f\"   Last Used: {stats['last_used'] or 'Never'}\\n\\n\"\n", "        \n", "        return report\n", "\n", "# Test performance monitoring\n", "print(\"🧪 Testing Performance Monitoring...\")\n", "print(\"=\" * 40)\n", "\n", "# Test with different models\n", "test_message = \"What is artificial intelligence in one sentence?\"\n", "\n", "for model_name in model_manager.get_available_models()[:2]:  # Test first 2 models\n", "    print(f\"\\n🤖 Testing {model_name}...\")\n", "    result = model_manager.invoke_with_monitoring(test_message, model_name)\n", "    \n", "    if result[\"success\"]:\n", "        print(f\"✅ Success in {result['metrics']['response_time']:.2f}s\")\n", "        print(f\"📝 Response: {result['response'][:100]}...\")\n", "    else:\n", "        print(f\"❌ Failed: {result['error']}\")\n", "\n", "# Show performance report\n", "print(\"\\n\" + model_manager.get_performance_report())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2.5: Model Comparison and Benchmarking\n", "\n", "**What we're doing**: Creating a systematic way to compare models\n", "\n", "**Why this is valuable**: Understand which models work best for different tasks\n", "\n", "**How it works**: Run the same prompts through different models and compare results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Model comparison and benchmarking\n", "def compare_models(model_manager: AIModelManager, test_prompts: List[str], models_to_test: Optional[List[str]] = None) -> Dict:\n", "    \"\"\"\n", "    Compare multiple models on the same set of prompts\n", "    \n", "    Args:\n", "        model_manager: The AI model manager instance\n", "        test_prompts: List of prompts to test\n", "        models_to_test: Specific models to test (optional)\n", "    \n", "    Returns:\n", "        Comparison results\n", "    \"\"\"\n", "    if models_to_test is None:\n", "        models_to_test = model_manager.get_available_models()\n", "    \n", "    results = {\n", "        \"prompts\": test_prompts,\n", "        \"models\": models_to_test,\n", "        \"responses\": {},\n", "        \"metrics\": {}\n", "    }\n", "    \n", "    print(f\"🔬 Comparing {len(models_to_test)} models on {len(test_prompts)} prompts...\")\n", "    print(\"=\" * 60)\n", "    \n", "    for i, prompt in enumerate(test_prompts):\n", "        print(f\"\\n📝 Prompt {i+1}: {prompt[:50]}...\")\n", "        results[\"responses\"][i] = {}\n", "        results[\"metrics\"][i] = {}\n", "        \n", "        for model_name in models_to_test:\n", "            print(f\"  🤖 Testing {model_name}...\")\n", "            \n", "            result = model_manager.invoke_with_monitoring(prompt, model_name)\n", "            \n", "            if result[\"success\"]:\n", "                results[\"responses\"][i][model_name] = result[\"response\"]\n", "                results[\"metrics\"][i][model_name] = result[\"metrics\"]\n", "                print(f\"    ✅ {result['metrics']['response_time']:.2f}s\")\n", "            else:\n", "                results[\"responses\"][i][model_name] = f\"ERROR: {result['error']}\"\n", "                results[\"metrics\"][i][model_name] = {\"error\": result[\"error\"]}\n", "                print(f\"    ❌ Failed\")\n", "    \n", "    return results\n", "\n", "# Define test prompts for comparison\n", "test_prompts = [\n", "    \"What is machine learning? Explain in one paragraph.\",\n", "    \"Write a haiku about programming.\",\n", "    \"Explain the difference between AI and ML.\",\n", "    \"What are the benefits of using Python for data science?\"\n", "]\n", "\n", "# Run comparison (limit to 2 models to save time)\n", "available_models = model_manager.get_available_models()\n", "models_to_compare = available_models[:2] if len(available_models) >= 2 else available_models\n", "\n", "if models_to_compare:\n", "    comparison_results = compare_models(model_manager, test_prompts[:2], models_to_compare)  # Test 2 prompts\n", "    \n", "    # Display comparison summary\n", "    print(\"\\n📊 Comparison Summary:\")\n", "    print(\"=\" * 40)\n", "    \n", "    for i, prompt in enumerate(comparison_results[\"prompts\"]):\n", "        print(f\"\\n📝 Prompt: {prompt[:40]}...\")\n", "        \n", "        for model_name in comparison_results[\"models\"]:\n", "            response = comparison_results[\"responses\"][i].get(model_name, \"No response\")\n", "            metrics = comparison_results[\"metrics\"][i].get(model_name, {})\n", "            \n", "            if \"error\" not in metrics:\n", "                response_time = metrics.get(\"response_time\", 0)\n", "                response_length = metrics.get(\"response_length\", 0)\n", "                print(f\"  🤖 {model_name}: {response_time:.2f}s, {response_length} chars\")\n", "                print(f\"     {response[:80]}...\")\n", "            else:\n", "                print(f\"  🤖 {model_name}: ERROR\")\n", "else:\n", "    print(\"❌ No models available for comparison\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎯 Step 2 Complete!\n", "\n", "### ✅ What We Accomplished\n", "1. **Model Characteristics**: Learned the strengths and weaknesses of different AI models\n", "2. **Professional Model Manager**: Built a class to manage multiple AI models\n", "3. **Intelligent Selection**: Created automatic model selection based on task requirements\n", "4. **Performance Monitoring**: Added tracking for response times and success rates\n", "5. **Model Comparison**: Built tools to systematically compare model performance\n", "\n", "### 🔧 Technical Skills Gained\n", "- **Object-Oriented Design**: Professional class architecture\n", "- **Strategy Pattern**: Switching between different implementations\n", "- **Performance Monitoring**: Tracking and analyzing system performance\n", "- **Error <PERSON>**: Graceful failure management\n", "- **Data Analysis**: Comparing and evaluating model performance\n", "\n", "### 🚀 Ready for Next Step\n", "You now have a professional AI model management system!\n", "\n", "**Next**: We'll move to **Step 3: Advanced Prompt Engineering** where we'll:\n", "- Build a library of professional prompt templates\n", "- Create dynamic prompts that adapt to context\n", "- Implement prompt optimization techniques\n", "- Add prompt versioning and A/B testing\n", "\n", "### 💡 Key Takeaways\n", "1. **Model Selection Matters**: Different models excel at different tasks\n", "2. **Performance Monitoring**: Always track what's working\n", "3. **Professional Architecture**: Good design makes systems maintainable\n", "4. **Fallback Strategies**: Plan for when things go wrong\n", "5. **Data-Driven Decisions**: Use metrics to choose the best approach\n", "\n", "### 🎯 Real-World Applications\n", "This model management system can be used for:\n", "- **Cost Optimization**: Use cheaper models for simple tasks\n", "- **Performance Optimization**: Use faster models when speed matters\n", "- **Quality Optimization**: Use better models for important tasks\n", "- **Reliability**: Automatic fallbacks when models fail\n", "- **Analytics**: Understanding usage patterns and performance\n", "\n", "Excellent work! You've built a production-ready AI model management system! 🎉"]}], "metadata": {"kernelspec": {"display_name": "agentic-2", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.18"}}, "nbformat": 4, "nbformat_minor": 2}