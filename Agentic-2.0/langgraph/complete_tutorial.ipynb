{"cells": [{"cell_type": "markdown", "id": "tutorial-header", "metadata": {}, "source": ["# **Complete LangGraph Tutorial: Building AI Workflows Step by Step**\n", "\n", "## **What Are We Learning?**\n", "\n", "**LangGraph** is a powerful framework for building **workflows** (step-by-step processes) for AI applications. Think of it like creating a factory assembly line where:\n", "- Each **station** (node) does a specific job\n", "- **Conveyor belts** (edges) move data between stations\n", "- The **factory manager** (graph) coordinates everything\n", "\n", "## **What Problem Are We Solving?**\n", "\n", "Instead of manually calling functions one by one, LangGraph helps us:\n", "1. **Automate workflows** - Let the system handle the sequence\n", "2. **Chain AI operations** - Connect multiple AI tasks together\n", "3. **Build complex applications** - Create sophisticated AI systems\n", "4. **Visualize processes** - See how data flows through our system\n", "\n", "## **Why Use LangGraph?**\n", "\n", "- **Organization**: Clean, structured code\n", "- **Reusability**: Build once, use many times\n", "- **Scalability**: Easy to add more steps\n", "- **Visualization**: See your workflow as a diagram\n", "- **Error <PERSON>**: Built-in error management\n", "\n", "---"]}, {"cell_type": "markdown", "id": "environment-setup", "metadata": {}, "source": ["## **Step 1: Environment Setup**\n", "\n", "First, let's make sure our environment is ready:"]}, {"cell_type": "code", "execution_count": null, "id": "environment-check", "metadata": {}, "outputs": [], "source": ["# Check if all required packages are installed\n", "try:\n", "    from langgraph.graph import Graph\n", "    from langchain_google_genai import ChatGoogleGenerativeAI\n", "    from IPython.display import Image, display\n", "    print(\"✅ All packages installed successfully!\")\n", "    print(\"✅ Ready to start learning LangGraph!\")\n", "except ImportError as e:\n", "    print(f\"❌ Missing package: {e}\")\n", "    print(\"Please run: pip install langgraph langchain-google-genai\")"]}, {"cell_type": "markdown", "id": "basic-concepts", "metadata": {}, "source": ["## **Step 2: Understanding Core Concepts**\n", "\n", "### **🔧 Key Components:**\n", "- **`Graph()`** - The workflow container (like a factory)\n", "- **`Nodes`** - Processing stations (functions that do work)\n", "- **`Edges`** - Connections between nodes (data flow paths)\n", "- **`Entry Point`** - Where the workflow starts\n", "- **`Finish Point`** - Where the workflow ends\n", "- **`invoke()`** - Run the workflow with input data\n", "- **`stream()`** - Run workflow and see step-by-step output\n", "\n", "### **📊 Flow Diagram:**\n", "```\n", "Input → [Node 1] → [Node 2] → [Node 3] → Output\n", "         ↓           ↓           ↓\n", "    Process A   Process B   Process C\n", "```"]}, {"cell_type": "markdown", "id": "example-1-intro", "metadata": {}, "source": ["## **Example 1: Simple Text Processing Workflow**\n", "\n", "Let's start with a simple example to understand the basics:"]}, {"cell_type": "code", "execution_count": null, "id": "simple-functions", "metadata": {}, "outputs": [], "source": ["# 🔧 FUNCTION 1: Text Processor\n", "def function1(input_text):\n", "    \"\"\"\n", "    First processing function in our workflow.\n", "    Adds a signature to the input text.\n", "    \"\"\"\n", "    result = input_text + \" from first function\"\n", "    print(f\"🔄 Function 1 processed: '{input_text}' → '{result}'\")\n", "    return result\n", "\n", "# 🔧 FUNCTION 2: Text Enhancer\n", "def function2(input_text):\n", "    \"\"\"\n", "    Second processing function in our workflow.\n", "    Adds another signature to the processed text.\n", "    \"\"\"\n", "    result = input_text + \" savita from second function\"\n", "    print(f\"🔄 Function 2 processed: '{input_text}' → '{result}'\")\n", "    return result\n", "\n", "print(\"✅ Functions defined successfully!\")"]}, {"cell_type": "code", "execution_count": null, "id": "test-functions", "metadata": {}, "outputs": [], "source": ["# 🧪 Test our functions individually\n", "print(\"Testing Function 1:\")\n", "test1 = function1(\"Hello\")\n", "print(f\"Result: {test1}\\n\")\n", "\n", "print(\"Testing Function 2:\")\n", "test2 = function2(\"World\")\n", "print(f\"Result: {test2}\")"]}, {"cell_type": "markdown", "id": "build-workflow", "metadata": {}, "source": ["## **Step 3: Building Our First Workflow**\n", "\n", "Now let's connect these functions using LangGraph:"]}, {"cell_type": "code", "execution_count": null, "id": "create-workflow", "metadata": {}, "outputs": [], "source": ["# 🏭 Step 1: Create an empty workflow\n", "workflow1 = Graph()\n", "print(\"✅ Empty workflow created!\")\n", "\n", "# 🔧 Step 2: Add nodes (processing stations)\n", "workflow1.add_node(\"fun1\", function1)\n", "workflow1.add_node(\"fun2\", function2)\n", "print(\"✅ Added processing nodes!\")\n", "\n", "# 🔗 Step 3: Add edges (connections)\n", "workflow1.add_edge(\"fun1\", \"fun2\")\n", "print(\"✅ Connected nodes with edges!\")\n", "\n", "# 🚪 Step 4: Set entry and exit points\n", "workflow1.set_entry_point(\"fun1\")\n", "workflow1.set_finish_point(\"fun2\")\n", "print(\"✅ Set entry and exit points!\")\n", "\n", "# ⚙️ Step 5: Compile the workflow\n", "app1 = workflow1.compile()\n", "print(\"✅ Workflow compiled and ready to use!\")"]}, {"cell_type": "markdown", "id": "visualize-workflow", "metadata": {}, "source": ["## **Step 4: Visualizing Our Workflow**\n", "\n", "Let's see what our workflow looks like:"]}, {"cell_type": "code", "execution_count": null, "id": "show-diagram", "metadata": {}, "outputs": [], "source": ["# 📊 Display the workflow diagram\n", "try:\n", "    display(Image(app1.get_graph().draw_mermaid_png()))\n", "    print(\"📊 Workflow diagram displayed above!\")\n", "except Exception as e:\n", "    print(f\"Note: Diagram visualization requires additional setup: {e}\")\n", "    print(\"But the workflow still works perfectly!\")"]}, {"cell_type": "markdown", "id": "run-workflow", "metadata": {}, "source": ["## **Step 5: Running Our Workflow**\n", "\n", "Now let's test our workflow:"]}, {"cell_type": "code", "execution_count": null, "id": "test-workflow", "metadata": {}, "outputs": [], "source": ["# 🚀 Run the workflow with simple invoke\n", "print(\"🚀 Running workflow with invoke():\")\n", "result = app1.invoke(\"Hello World\")\n", "print(f\"\\n📤 Final Result: {result}\")"]}, {"cell_type": "code", "execution_count": null, "id": "stream-workflow", "metadata": {}, "outputs": [], "source": ["# 🔄 Run the workflow with streaming to see each step\n", "print(\"🔄 Running workflow with stream() to see each step:\")\n", "print(\"=\" * 50)\n", "\n", "for output in app1.stream(\"Learning LangGraph\"):\n", "    for node_name, result in output.items():\n", "        print(f\"📍 Output from {node_name}:\")\n", "        print(f\"   {result}\")\n", "        print(\"-\" * 30)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}